/* 

--------------TYPOGRAPHY------------------
FONT SIZES
12px / 16px / 20px / 24px / 32px / 40px

FONT WEIGHTS
400 / 700

LINE HEIGHTS
1.2 / 1.5

LETTER SPACING
0 / -2px

FONT FAMILY
Poppins

-------------COLORS-----------------------

PRIMPARY
Base: #339af0 {Sky Blue}
Tint: #d0cbff {Arctic}
Shade: #1c7ed6 {Deep Sea}

SECONDARY
Base: #9775fa {<PERSON><PERSON><PERSON>}
Tint: #e5dbff {Moonlight Iris}
Shade: #7048e8 {Velvet Night}

TERTIARY
Base: #ff922b {Pumpkin}
Tint: #ffe8cc {Dawn}
Shade: #f76707 {Lava}

GREY
Base: #495057 {Slate}
Tint: #f1f3f5 {Pebble}
Shade: #212529 {Graphite}

-------------------- B<PERSON>DER RADIUS -------------------

4px / 8px / 20px



-------------------- SPACING AREA -------------------


*/




/*---------------- GLOBAL STYPES -------------------- */

body { 
    font-weight: 400;
    line-height: 1.5;
    font-family: 'poppins', sans-serif;
    color: #495057;
}

h1, h2, h3, h4, h5 {
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -2px;
    font-family: 'poppins', sans-serif;
    color: #212529;
}

h1 {
    font-size: 62px;
}

h2 {
    font-size: 48px;
}

h3 {
    text-align: left;
    font-size: 40px;
}

p {
    font-size: 20px;
}

a {
    text-decoration: none;
    font-size: 20px;
    display: inline-block;
}

ul {
    list-style: none;
}

span {
    display: inline-block;
}

.orange-text {
   color: #ff922b
}

.small-text{
    font-size: 12px;
}

/*---------------- COMPNENTS -------------------- */

.btn {
    font-size: 16px;
    text-decoration: none;
    padding: 15px 30px;
    border-radius: 8px;
}

.btn-primary:link, .btn-primary:visited {
    color: #ffffff;
    background-color: #339af0;
    border: 2px solid #339af0;
}

.btn-primary:hover, .btn-primary:active {
    color: #ffffff;
    background-color: #1c7ed6;
    border: 2px solid #1c7ed6;
}

.btn-secondary:link, .btn-secondary:visited{
    color: #ffffff;
    background-color: #9775fa;
    border: 2px solid #9775fa;
}

.btn-seoondary:hover, .btn-secondary:active{
    color: #ffffff;
    background-color: #7048e8;
    border: 2px solid #7048e8;
}

.btn-primary-outline:link, .btn-primary-outline:visited {
    color: #339af0;
    background-color: #ffffff;
    border: 2px solid #339af0;
}

.btn-primary-outline:hover, .btn-primary-outline:active {
    color: #1c7ed6;
    background-color: #ffffff;
    border: 2px solid #1c7ed6;
}


.highlight {
    color: #ffffff;
    padding: 5px 10px;
    border-radius: 4px;
}

.hightlight-primary {
    background-color: #339af0;
}
.highlight-secondary {
    background-color: #9775fa;
}

.hightlight-tertiary {
    background-color: #ff922b;
}

.logo-md {
    width: 150px;
}

.logo-sm {
    width: 80px;
}


/*---------------- SECTIONS -------------------- */

/* -------------------- NAV BAR --------------------- */

.navbar {
    margin-bottom: 200px;
}

/*---------------- HERO SECTION -------------------- */
.hero-section {
    text-align: center;
    margin-bottom: 300px;
}

.hero-heading {
    margin-bottom: 10px;
}

.hero-paragraph {
    margin-bottom: 50px;
}
/*---------------- FEATURE SECTION -------------------- */

.feature-section {
    margin-bottom: 300px;
}

.features-main-heading{
    text-align: center;
    margin-bottom: 80px;
}

.feature-img {
    width: 400px;
}

/*---------------- CTA SECTION -------------------- */
.cta-section {
    text-align: center;
    margin-bottom: 300px;
}

.cta-card {
    text-align: center;
    background-color: #339af0;
    width: 1000px;
    padding: 100px 200px;
    border-radius: 20px;
}

/*---------------- FOOTER SECTION -------------------- */
.footer {
    text-align: center;
}

.social-icons {
    width: 20px;
}