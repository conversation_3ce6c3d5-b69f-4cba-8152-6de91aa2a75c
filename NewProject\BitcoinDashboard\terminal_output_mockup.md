# Terminal Output Mockup

Below is a representation of what the Bitcoin Dashboard would look like when running in a terminal with ANSI color support:

```
╔══════════════════════════════════════════════════════════╗
║                   BITCOIN DASHBOARD                       ║
║                     PHOENIXBIT                           ║
╚══════════════════════════════════════════════════════════╝

▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓
SUMMARY METRICS
▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓

Total Bitcoin on Corporate Balance Sheets: 1456789 BTC
Number of Companies Holding Bitcoin: 42
Percentage of Bitcoin Supply: 6.9%

▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓
TOP COMPANIES BY BITCOIN HOLDINGS
▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓

COMPANY              TICKER BTC HOLDINGS    % TREASURY FIRST PURCHASE
────────────────────────────────────────────────────────────────────
MicroStrategy        MSTR   140000          95.0%      2020-08-11
Tesla                TSLA   42000           15.0%      2021-02-08
Galaxy Digital       GLXY   16400           70.0%      2018-11-05
Marathon Digital     MARA   13380           80.0%      2021-01-15
Coinbase             COIN   9000            40.0%      2019-05-20

▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓
INDUSTRY BREAKDOWN
▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓

INDUSTRY BREAKDOWN (% of total holdings)
────────────────────────────────────────────────────────────────────
Software             ████████████████ 32.0%
Financial Technology ██████████████ 28.0%
Mining               ███████ 15.0%
Automotive           ████ 8.0%
Other                ████████ 17.0%

▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓
HISTORICAL GROWTH
▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓

HISTORICAL BITCOIN HOLDINGS GROWTH (in thousands BTC)
────────────────────────────────────────────────────────────────────
2020-08: ■■■ 85
2020-12: ■■■■■■■■ 225
2021-03: ■■■■■■■■■■■■■■■■■■■■■■■■■■■■ 815
2021-06: ■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■ 1120
2021-12: ■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■ 1320
2022-06: ■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■ 1380
2022-12: ■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■ 1410
2023-06: ■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■■ 1457

Press Enter to exit...
```

## Color Legend

In the actual terminal:
- **Bitcoin Orange** (#F7931A) would be used for:
  - Dashboard header
  - Section dividers
  - Chart bars and visualizations
  - Key metrics and numbers

- **Black** (#121212) would be used for:
  - Background (in a GUI version)

- **Gray** (#808080) would be used for:
  - Labels
  - Table dividers
  - Secondary information

- **White** (#FFFFFF) would be used for:
  - Section titles
  - Primary text
  - Table headers

## Interactive Elements

In a fully implemented version, the user would be able to:
1. Navigate between different sections using keyboard shortcuts
2. Sort the companies table by different columns
3. Filter data based on various criteria
4. View detailed information about specific companies
5. Update data from external sources

## Visual Enhancements

A more advanced implementation could include:
- More sophisticated charts using Unicode block characters
- Company logos or Bitcoin symbols as visual elements
- Progress bars for loading data
- Animated transitions between views
- Color gradients for visualizations
