1 # Welcome to your RPG!
    2 # Chapter 1: The Journey Begins with Decisions
    3
    4 # We'll use everything we've learned: print(), input(), variables, and f-strings.
    5 # NEW CONCEPT: if/elif/else statements. They let your program make choices.
    6
    7 # --- Character Creation ---
    8 player_name = input("What is your name, brave adventurer? ")
    9 print(f"Welcome, {player_name}! Your journey is about to begin.")
   10
   11 # --- The First Choice ---
   12 print("\nYou stand at a crossroads. A sign points in two directions.")
   13 print("1. Go to the dark forest.")
   14 print("2. Go to the sunny meadow.")
   15 choice = input("What is your choice? (Enter 1 or 2): ")
   16
   17 # --- Handling the Choice with if/elif/else ---
   18 # The `if` statement checks a condition. If it's true, the indented code below it runs.
   19 # The `==` is a comparison operator. It checks if two things are exactly equal.
   20 if choice == "1":
   21     print(f"\n{player_name}, you venture into the dark forest.")
   22     print("You hear a spooky sound and feel a chill down your spine.")
   23
   24 # `elif` is "else if". It checks another condition if the first `if` was false.
   25 elif choice == "2":
   26     print(f"\n{player_name}, you walk into the sunny meadow.")
   27     print("You see beautiful flowers and hear birds singing.")
   28
   29 # `else` is the catch-all. It runs if NONE of the above conditions were true.
   30 else:
   31     print("\nInvalid choice! You stumble around and waste the day.")
   32     print("The adventure ends before it begins.")