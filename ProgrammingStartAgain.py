# Welcome to your RPG!
# Chapter 1: The Journey Begins with Decisions
import random

# --- Character Creation ---
player_name = input("What is your name, brave adventurer? ")
monster_name = "goblin"
monster_health = 50
player_health = 100
player_inventory = ["sword", "shield"]
player_gold = 0
player_exp = 0
player_level = 1
player_attack = 10
player_defense = 10
player_speed = 10
player_luck = 10
player_intelligence = 10
player_charisma = 10
player_wisdom = 10
player_strength = 10
player_dexterity = 10
player_constitution = 10
player_perception = 10
player_charisma = 10
player_willpower = 10
player_endurance = 10

print(f"Welcome, {player_name}! Your journey is about to begin.")


# --- The First Choice ---
print("\nYou stand at a crossroads. A sign points in three different directions. Two paths are clearly marked, while the third is just a faint whisper in the wind. Choose your path wisely, for your choices will shape your destiny.")
print("1. Go to the dark forest.")
print("2. Go to the sunny meadow.")
print("3. Follow the sound")
choice = input("What is your choice? (Enter 1 or 2 or 3): ")

# --- Handling the Choice with if/elif/else ---
# The `if` statement checks a condition. If it's true, the indented code below it runs.
# The `==` is a comparison operator. It checks if two things are exactly equal.
if choice == "1":
    print(f"\n{player_name}, you venture into the dark forest.")
    print("You hear a spooky sound and feel a chill down your spine.")
    print("Follow the sound")

# `elif` is "else if". It checks another condition if the first `if` was false.
elif choice == "2":
    print(f"\n{player_name}, you walk into the sunny meadow.")
    print("You see beautiful flowers and hear birds singing.")

elif choice == "3":
    print(f"\n{player_name}, you walk towards the sound.")
    print("You follow the sound and find a hidden cave.")

# `else` is the catch-all. It runs if NONE of the above conditions were true.
else:
    print("\nInvalid choice! You stumble around and waste the day.")
    print("The adventure ends before it begins.")

print(f"\n--- A wild {monster_name} appears! ---")
   13
   14 # --- The Game Loop ---
   15 # This `while` loop will continue as long as the condition is True.
   16 # This creates our "battle loop".
   17 while True:
   18     # Print the status of the battle at the start of each turn.
   19     print(f"\nYour Health: {player_health} | {monster_name}'s Health: {monster_health}")
   20
   21     # Get the player's action.
   22     action = input("What do you do? (Enter 'attack' or 'run'): ")
   23
   24     # --- Player's Turn ---
   25     if action == "attack":
   26         player_damage = random.randint(10, 20) # random number between 10 and 20
   27         monster_health = monster_health - player_damage
   28         print(f"You attack the {monster_name} for {player_damage} damage!")
   29
   30         # Check if the monster was defeated.
   31         if monster_health <= 0:
   32             print(f"You have defeated the {monster_name}!")
   33             break # The `break` keyword immediately exits the loop.
   34
   35     elif action == "run":
   36         print("You decided to run away. The battle is over.")
   37         break # Exit the loop.
   38
   39     else:
   40         print("Invalid action! You hesitate and do nothing.")
   41
   42     # --- Monster's Turn (if the battle isn't over) ---
   43     monster_damage = random.randint(5, 15)
   44     player_health = player_health - monster_damage
   45     print(f"The {monster_name} attacks you for {monster_damage} damage!")
   46
   47     # Check if the player was defeated.
   48     if player_health <= 0:
   49         print("You have been defeated...")
   50         break # Exit the loop.
   51
   52 print("\n--- End of Battle ---")