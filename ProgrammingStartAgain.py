greeting = "Hello from <PERSON>!"
print(greeting)

student_count = 1
new_student = "<PERSON>"
month = "September"
course_name = "Python Programming"
price = 99.95

print(f"Welcome to the {course_name}! We have {student_count} students. We are in month {month}")


    # --- Getting User Input ---
    # The input() function prompts the user and captures their response as a string.
    
    print(f"--- Part 1: Basic Input ---")
    user_name = input("Please enter your name: ")
    print(f"Hello, {user_name}! Welcome.")
    
    
    # --- The Problem with Input and Numbers ---
 # What happens when we try to do math?
    print("\n--- Part 2: The Problem with Math ---")
    num1_str = input("Enter a number: ")
    num2_str = input("Enter another number: ")
    result_wrong = num1_str + num2_str
   
    # The '+' operator on two strings doesn't add them, it concatenates them (sticks them together).
    print(f"If we just use '+', the result is '{result_wrong}'. This is not what we want!")
   

   # --- The Solution: Type Casting ---
   # We must convert the string from the input() function into a number type (like int or float).
   # This is called Type Casting. We use functions like int(), float(), str().
    print("\n--- Part 3: The Solution with Type Casting ---")
    num1_int = int(num1_str)  # Convert the first number to an integer
    num2_int = int(num2_str)  # Convert the second number to an integer
    result_correct = num1_int + num2_int
   
   print(f"After converting to integers, the correct sum is: {result_correct}")
