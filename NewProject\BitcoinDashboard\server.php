<?php
// Simple PHP server for the Bitcoin Dashboard
$port = 8000;
$host = "localhost";

echo "[SERVER] Starting PHP server at http://$host:$port\n";
echo "[SERVER] Press Ctrl+C to stop the server\n";

// Open browser
$url = "http://$host:$port/dashboard.html";
if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
    // Windows
    pclose(popen("start $url", "r"));
} elseif (strtoupper(substr(PHP_OS, 0, 3)) === 'DAR') {
    // macOS
    pclose(popen("open $url", "r"));
} else {
    // Linux
    pclose(popen("xdg-open $url", "r"));
}

// Start server
passthru("php -S $host:$port");
?>
