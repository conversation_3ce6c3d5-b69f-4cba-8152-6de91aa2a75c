<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrainerScripts - Professional Communication Templates for Personal Trainers</title>

    <!-- Google Analytics 4 Tracking Code -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-XXXXXXXXXX');
    </script>

    <!-- Tailwind CSS CDN for lean development -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#F7931A', // Bitcoin orange as primary color
                        dark: '#1A1A1A',
                        light: '#F3F4F6'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-light text-dark font-sans">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm">
        <div class="max-w-6xl mx-auto px-4 py-3 flex justify-between items-center">
            <div class="font-bold text-2xl text-primary">TrainerScripts</div>
            <div class="space-x-4">
                <a href="#features" class="hover:text-primary">Features</a>
                <a href="#pricing" class="hover:text-primary">Pricing</a>
                <a href="#faq" class="hover:text-primary">FAQ</a>
                <a href="#early-access" class="bg-primary text-white px-4 py-2 rounded hover:bg-opacity-90" onclick="gtag('event', 'click', {'event_category': 'navigation', 'event_label': 'nav_early_access'});">Early Access</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <header class="bg-white py-16">
        <div class="max-w-6xl mx-auto px-4 flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 mb-8 md:mb-0">
                <h1 class="text-5xl font-bold mb-4">Stop Winging Difficult Client Conversations</h1>
                <p class="text-2xl mb-4 text-gray-700">15 professional, ready-to-use scripts that turn awkward trainer-client moments into breakthrough opportunities.</p>
                <p class="text-xl mb-6 text-primary font-semibold">Because the right words at the right time can transform a client's entire journey.</p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="#early-access" class="bg-primary text-white px-8 py-4 rounded-lg text-lg font-bold hover:bg-opacity-90 transition-all transform hover:scale-105" onclick="gtag('event', 'click', {'event_category': 'cta', 'event_label': 'hero_early_access'});">Get 50% Off Launch Price</a>
                    <a href="#how-it-works" class="border-2 border-primary text-primary px-8 py-4 rounded-lg text-lg font-bold hover:bg-primary hover:text-white transition-all" onclick="gtag('event', 'click', {'event_category': 'cta', 'event_label': 'hero_how_it_works'});">See How It Works</a>
                </div>
                <div class="mt-6 flex items-center text-sm text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span>Join <strong>127 trainers</strong> already on the early access list</span>
                </div>
            </div>
            <div class="md:w-1/2">
                <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Personal trainer with client" class="rounded-lg shadow-lg">
            </div>
        </div>
    </header>

    <!-- Features Section -->
    <section id="features" class="py-16 bg-light">
        <div class="max-w-6xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Why Trainers Love Our Scripts</h2>

            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="text-primary text-4xl mb-4">📝</div>
                    <h3 class="text-xl font-semibold mb-2">Professional Templates</h3>
                    <p>Evidence-based scripts for discussing nutrition, progress plateaus, and motivation with clients.</p>
                </div>

                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="text-primary text-4xl mb-4">🎨</div>
                    <h3 class="text-xl font-semibold mb-2">Multi-Theme Design</h3>
                    <p>Customize the look and feel to match your brand with multiple theme options.</p>
                </div>

                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="text-primary text-4xl mb-4">⏱️</div>
                    <h3 class="text-xl font-semibold mb-2">Save Time</h3>
                    <p>Stop writing the same emails and messages. Customize our templates in seconds.</p>
                </div>

                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="text-primary text-4xl mb-4">📊</div>
                    <h3 class="text-xl font-semibold mb-2">Client Management</h3>
                    <p>Store client information and communication history in one secure place.</p>
                </div>

                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="text-primary text-4xl mb-4">📱</div>
                    <h3 class="text-xl font-semibold mb-2">Works Everywhere</h3>
                    <p>Access your scripts on any device - desktop, tablet, or mobile.</p>
                </div>

                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="text-primary text-4xl mb-4">🔄</div>
                    <h3 class="text-xl font-semibold mb-2">Regular Updates</h3>
                    <p>New templates added monthly based on trainer feedback and industry trends.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="py-16 bg-white">
        <div class="max-w-6xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-4">How TrainerScripts Works</h2>
            <p class="text-xl text-center text-gray-600 mb-12 max-w-3xl mx-auto">From awkward conversation to client breakthrough in 3 simple steps</p>

            <div class="grid md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="bg-primary bg-opacity-10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-primary text-2xl font-bold">1</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Choose Your Script</h3>
                    <p class="text-gray-600">Select from 15 professionally crafted scripts for different client scenarios - from motivation issues to nutrition discussions.</p>
                    <img src="https://images.unsplash.com/photo-1512758017271-d7b84c2113f1?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80" alt="Choose script" class="rounded-lg mt-4 mx-auto">
                </div>

                <div class="text-center">
                    <div class="bg-primary bg-opacity-10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-primary text-2xl font-bold">2</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Personalize It</h3>
                    <p class="text-gray-600">Customize the script with your client's name, specific situation, and your personal training style in just minutes.</p>
                    <img src="https://images.unsplash.com/photo-1571867424488-4565932edb41?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80" alt="Personalize script" class="rounded-lg mt-4 mx-auto">
                </div>

                <div class="text-center">
                    <div class="bg-primary bg-opacity-10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-primary text-2xl font-bold">3</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Deliver With Confidence</h3>
                    <p class="text-gray-600">Use your personalized script during sessions, emails, or messages to communicate professionally and get better client results.</p>
                    <img src="https://images.unsplash.com/photo-1517836357463-d25dfeac3438?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80" alt="Deliver script" class="rounded-lg mt-4 mx-auto">
                </div>
            </div>
        </div>
    </section>

    <!-- Preview Section -->
    <section class="py-16 bg-light">
        <div class="max-w-6xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">See It In Action</h2>

            <div class="grid md:grid-cols-2 gap-6 mb-8">
                <div class="bg-light p-6 rounded-lg shadow-lg">
                    <h3 class="text-xl font-semibold mb-4">Weight Loss Plateau Discussion</h3>
                    <div class="border-l-4 border-primary pl-4 py-2 mb-4">
                        <p class="italic">"[Client Name], you've been making great progress toward your weight loss goal. I've noticed your results have slowed down a bit over the past two weeks. Let's work together to identify what might be happening and make some adjustments to keep you moving forward. How have you been feeling about your progress lately?"</p>
                    </div>
                    <p class="text-sm text-gray-600">This template helps trainers address weight loss plateaus in a supportive, non-judgmental way that maintains client motivation.</p>
                </div>

                <div class="bg-light p-6 rounded-lg shadow-lg">
                    <h3 class="text-xl font-semibold mb-4">Motivation During Plateaus</h3>
                    <div class="border-l-4 border-primary pl-4 py-2 mb-4">
                        <p class="italic">"[Client Name], plateaus are actually confirmation that your previous efforts have worked—your body has successfully adapted to the challenges we've given it. Now it's simply time to introduce new challenges. Think of it this way: if you were learning piano, you wouldn't keep playing only beginner songs forever. You'd move to more challenging pieces to continue improving."</p>
                    </div>
                    <p class="text-sm text-gray-600 mb-3">This comprehensive script helps trainers reframe plateaus as signs of success and provides a structured approach to breaking through them.</p>
                    <a href="TrainerScripts/Motivation_During_Plateau.html" class="text-primary hover:underline text-sm">View full template →</a>
                </div>
            </div>

            <div class="text-center mb-8">
                <a href="#early-access" class="inline-block bg-primary text-white px-6 py-3 rounded-lg text-lg font-medium hover:bg-opacity-90 transform hover:scale-105 transition-all">Access All 30 Templates</a>
            </div>

            <div class="bg-primary bg-opacity-10 p-6 rounded-lg mb-16">
                <div class="flex items-center justify-center mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    <h3 class="text-2xl font-bold text-primary">NEW: Expanded to 30 Professional Scripts!</h3>
                </div>
                <p class="text-center text-gray-700 mb-4">We've doubled our template library to cover even more critical client scenarios</p>

                <div class="grid md:grid-cols-3 gap-3 mb-4">
                    <div class="bg-white p-3 rounded shadow-sm">
                        <p class="font-semibold">Breaking Through Fitness Burnout</p>
                        <p class="text-sm text-gray-600">Reignite motivation in long-term clients</p>
                        <a href="TrainerScripts/Breaking_Through_Fitness_Burnout.html" class="text-primary text-sm hover:underline">View template →</a>
                    </div>
                    <div class="bg-white p-3 rounded shadow-sm">
                        <p class="font-semibold">Discussing Progress Beyond Weight</p>
                        <p class="text-sm text-gray-600">Redirect focus to meaningful metrics</p>
                    </div>
                    <div class="bg-white p-3 rounded shadow-sm">
                        <p class="font-semibold">Handling Overtraining Risk</p>
                        <p class="text-sm text-gray-600">Address clients pushing too hard</p>
                    </div>
                    <div class="bg-white p-3 rounded shadow-sm">
                        <p class="font-semibold">Nutrition Conversations</p>
                        <p class="text-sm text-gray-600">Stay within scope of practice</p>
                    </div>
                    <div class="bg-white p-3 rounded shadow-sm">
                        <p class="font-semibold">Reengaging Lapsed Clients</p>
                        <p class="text-sm text-gray-600">Reconnect without judgment</p>
                    </div>
                    <div class="bg-white p-3 rounded shadow-sm">
                        <p class="font-semibold">+ 20 More Templates</p>
                        <p class="text-sm text-gray-600">Covering every client scenario</p>
                        <a href="TrainerScripts/EXPANDED_CATALOG.md" class="text-primary text-sm hover:underline">View full catalog →</a>
                    </div>
                </div>
            </div>

            <!-- Testimonials -->
            <h3 class="text-2xl font-bold text-center mb-8">What Trainers Are Saying</h3>

            <div class="grid md:grid-cols-3 gap-6">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center mb-4">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Trainer testimonial" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-bold">Michael T.</h4>
                            <p class="text-sm text-gray-600">Personal Trainer, 8 years</p>
                        </div>
                    </div>
                    <div class="mb-3">
                        <span class="text-yellow-400">★★★★★</span>
                    </div>
                    <p class="italic text-gray-700">"The nutrition plateau script saved a client relationship that was on the rocks. Instead of my usual awkward conversation, I had a structured approach that actually resonated with them."</p>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center mb-4">
                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Trainer testimonial" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-bold">Sarah K.</h4>
                            <p class="text-sm text-gray-600">Fitness Studio Owner</p>
                        </div>
                    </div>
                    <div class="mb-3">
                        <span class="text-yellow-400">★★★★★</span>
                    </div>
                    <p class="italic text-gray-700">"I've required all trainers at my studio to use these scripts. Client retention is up 22% since implementing them, and our trainers feel more confident handling difficult conversations."</p>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center mb-4">
                        <img src="https://randomuser.me/api/portraits/men/67.jpg" alt="Trainer testimonial" class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-bold">James W.</h4>
                            <p class="text-sm text-gray-600">Online Fitness Coach</p>
                        </div>
                    </div>
                    <div class="mb-3">
                        <span class="text-yellow-400">★★★★★</span>
                    </div>
                    <p class="italic text-gray-700">"As someone who's better with workouts than words, these scripts have been a game-changer for my online coaching business. Worth every penny for the time saved and clients retained."</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-16 bg-light">
        <div class="max-w-6xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Simple, Transparent Pricing</h2>

            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="text-center mb-6">
                        <h3 class="text-xl font-bold">Free</h3>
                        <div class="text-4xl font-bold my-4">$0<span class="text-sm font-normal text-gray-600">/month</span></div>
                        <p class="text-gray-600">For individual trainers just starting out</p>
                    </div>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> 5 basic templates</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Single theme</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> PDF export</li>
                        <li class="flex items-center opacity-50"><span class="text-red-500 mr-2">✗</span> Client management</li>
                        <li class="flex items-center opacity-50"><span class="text-red-500 mr-2">✗</span> Custom branding</li>
                    </ul>
                    <a href="#early-access" class="block text-center bg-gray-200 text-dark px-4 py-2 rounded hover:bg-gray-300" onclick="gtag('event', 'click', {'event_category': 'pricing', 'event_label': 'free_tier'});">Sign Up</a>
                </div>

                <div class="bg-primary text-white p-6 rounded-lg shadow transform scale-105">
                    <div class="absolute top-0 right-0 bg-yellow-400 text-dark text-xs font-bold px-3 py-1 rounded-bl-lg rounded-tr-lg">POPULAR</div>
                    <div class="text-center mb-6">
                        <h3 class="text-xl font-bold">Pro</h3>
                        <div class="text-4xl font-bold my-4">$19<span class="text-sm font-normal text-white opacity-80">/month</span></div>
                        <p class="text-white opacity-80">For serious trainers with multiple clients</p>
                    </div>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center"><span class="text-white mr-2">✓</span> All 30 professional templates</li>
                        <li class="flex items-center"><span class="text-white mr-2">✓</span> Multiple themes</li>
                        <li class="flex items-center"><span class="text-white mr-2">✓</span> PDF & email export</li>
                        <li class="flex items-center"><span class="text-white mr-2">✓</span> Client management (up to 50)</li>
                        <li class="flex items-center"><span class="text-white mr-2">✓</span> Basic branding</li>
                    </ul>
                    <a href="#early-access" class="block text-center bg-white text-primary px-4 py-2 rounded hover:bg-opacity-90" onclick="gtag('event', 'click', {'event_category': 'pricing', 'event_label': 'pro_tier'});">Get Started</a>
                </div>

                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="text-center mb-6">
                        <h3 class="text-xl font-bold">Business</h3>
                        <div class="text-4xl font-bold my-4">$39<span class="text-sm font-normal text-gray-600">/month</span></div>
                        <p class="text-gray-600">For fitness studios and teams</p>
                    </div>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> All Pro features</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Unlimited clients</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Team collaboration</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Custom templates</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> White labeling</li>
                    </ul>
                    <a href="#early-access" class="block text-center bg-gray-200 text-dark px-4 py-2 rounded hover:bg-gray-300" onclick="gtag('event', 'click', {'event_category': 'pricing', 'event_label': 'business_tier'});">Contact Sales</a>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" class="py-16 bg-white">
        <div class="max-w-3xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">Frequently Asked Questions</h2>

            <div class="space-y-6">
                <div>
                    <h3 class="text-xl font-semibold mb-2">How customizable are the templates?</h3>
                    <p>All templates are fully customizable. You can edit any text, add your own sections, and save your customized versions for future use.</p>
                </div>

                <div>
                    <h3 class="text-xl font-semibold mb-2">Can I use these templates for email and in-person conversations?</h3>
                    <p>Yes! Our templates are designed to work for both written communication (email, messaging) and as scripts for in-person or video consultations.</p>
                </div>

                <div>
                    <h3 class="text-xl font-semibold mb-2">Are the templates evidence-based?</h3>
                    <p>Absolutely. All our templates are created by fitness professionals with input from communication experts and are based on proven client communication strategies.</p>
                </div>

                <div>
                    <h3 class="text-xl font-semibold mb-2">Can I cancel my subscription anytime?</h3>
                    <p>Yes, you can cancel your subscription at any time. There are no long-term contracts or cancellation fees.</p>
                </div>

                <div>
                    <h3 class="text-xl font-semibold mb-2">Do you offer a free trial?</h3>
                    <p>Yes, we offer a 14-day free trial of our Pro plan so you can experience the full benefits before committing.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Early Access Section -->
    <section id="early-access" class="py-16 bg-primary text-white">
        <div class="max-w-4xl mx-auto px-4 text-center">
            <div class="bg-dark bg-opacity-20 inline-block px-4 py-2 rounded-full text-sm font-bold mb-4">LIMITED TIME OFFER</div>
            <h2 class="text-4xl font-bold mb-4">Early Access Closing Soon</h2>
            <p class="text-xl mb-8 max-w-2xl mx-auto">Join the founding members and get <span class="font-bold underline">50% off for life</span> plus exclusive access to new scripts before they're released.</p>

            <!-- Countdown Timer -->
            <div class="flex justify-center mb-8">
                <div class="grid grid-cols-4 gap-4 text-center">
                    <div class="bg-dark bg-opacity-30 rounded-lg p-4">
                        <div class="text-3xl font-bold" id="days">3</div>
                        <div class="text-xs uppercase tracking-wide">Days</div>
                    </div>
                    <div class="bg-dark bg-opacity-30 rounded-lg p-4">
                        <div class="text-3xl font-bold" id="hours">11</div>
                        <div class="text-xs uppercase tracking-wide">Hours</div>
                    </div>
                    <div class="bg-dark bg-opacity-30 rounded-lg p-4">
                        <div class="text-3xl font-bold" id="minutes">24</div>
                        <div class="text-xs uppercase tracking-wide">Minutes</div>
                    </div>
                    <div class="bg-dark bg-opacity-30 rounded-lg p-4">
                        <div class="text-3xl font-bold" id="seconds">07</div>
                        <div class="text-xs uppercase tracking-wide">Seconds</div>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg p-8 shadow-lg mb-8 max-w-2xl mx-auto">
                <h3 class="text-primary text-2xl font-bold mb-4">Reserve Your Spot Now</h3>
                <p class="text-gray-600 mb-6">Only <strong>23 spots</strong> remaining at the founding member price.</p>

                <form id="waitlist-form" class="mb-4" onsubmit="gtag('event', 'form_submission', {'event_category': 'lead', 'event_label': 'waitlist_signup'}); return false;">
                    <div class="flex flex-col sm:flex-row gap-2 mb-4">
                        <input type="text" placeholder="Your name" class="flex-grow px-4 py-3 rounded border border-gray-300 text-dark" required>
                        <input type="email" placeholder="Your email address" class="flex-grow px-4 py-3 rounded border border-gray-300 text-dark" required>
                    </div>
                    <button type="submit" class="w-full bg-primary text-white px-6 py-4 rounded-lg font-bold text-lg hover:bg-opacity-90 transition-all transform hover:scale-105">Secure 50% Lifetime Discount</button>
                </form>

                <div class="flex items-center justify-center text-sm text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    <span>Your information is secure and will never be shared</span>
                </div>
            </div>

            <div class="flex flex-col md:flex-row items-center justify-center gap-6 text-sm">
                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span>50% lifetime discount</span>
                </div>
                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span>Early access to new scripts</span>
                </div>
                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span>30-day money-back guarantee</span>
                </div>
            </div>

            <script>
                // Form submission handling
                document.getElementById('waitlist-form').addEventListener('submit', function(e) {
                    e.preventDefault();
                    const email = this.querySelector('input[type="email"]').value;
                    const name = this.querySelector('input[type="text"]').value;

                    // In a real implementation, you would send this to your backend
                    console.log('Submission:', { name, email });

                    // Show success message
                    alert('Thanks for joining our founding members! Your 50% lifetime discount has been reserved. We\'ll be in touch soon with your exclusive access details.');

                    // Clear the form
                    this.reset();
                });

                // Countdown timer
                function updateCountdown() {
                    // Set the date we're counting down to (3 days from now)
                    const countDownDate = new Date();
                    countDownDate.setDate(countDownDate.getDate() + 3);

                    // Update the countdown every 1 second
                    const x = setInterval(function() {
                        // Get current date and time
                        const now = new Date().getTime();

                        // Find the distance between now and the countdown date
                        const distance = countDownDate - now;

                        // Time calculations for days, hours, minutes and seconds
                        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                        // Display the result
                        document.getElementById("days").innerHTML = days;
                        document.getElementById("hours").innerHTML = hours;
                        document.getElementById("minutes").innerHTML = minutes;
                        document.getElementById("seconds").innerHTML = seconds < 10 ? "0" + seconds : seconds;

                        // If the countdown is finished, display expiration message
                        if (distance < 0) {
                            clearInterval(x);
                            document.getElementById("days").innerHTML = "0";
                            document.getElementById("hours").innerHTML = "0";
                            document.getElementById("minutes").innerHTML = "0";
                            document.getElementById("seconds").innerHTML = "00";
                        }
                    }, 1000);
                }

                // Initialize countdown
                updateCountdown();
            </script>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-8">
        <div class="max-w-6xl mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <div class="font-bold text-xl text-primary">TrainerScripts</div>
                    <p class="text-sm opacity-80">© 2023 PhoenixBit. All rights reserved.</p>
                </div>
                <div class="flex space-x-4">
                    <a href="#" class="opacity-80 hover:opacity-100">Terms</a>
                    <a href="#" class="opacity-80 hover:opacity-100">Privacy</a>
                    <a href="#" class="opacity-80 hover:opacity-100">Contact</a>
                </div>
            </div>
        </div>
    </footer>

    <!--
    INSTRUCTIONS FOR SETTING UP GOOGLE ANALYTICS:

    1. Create a Google Analytics 4 property at https://analytics.google.com/
    2. Get your Measurement ID (format: G-XXXXXXXXXX)
    3. Replace the placeholder "G-XXXXXXXXXX" in the Google Analytics script with your actual Measurement ID
    4. Verify that data is being collected in your Google Analytics dashboard

    ANALYTICS IMPLEMENTATION DETAILS:

    This landing page tracks the following events:

    1. Page Views: Automatic tracking of page visits
    2. Navigation Clicks: When users click navigation items
    3. CTA Clicks: When users click call-to-action buttons
    4. Pricing Interactions: When users engage with pricing options
    5. Form Submissions: When users join the waitlist

    You can view these events in the "Events" section of your Google Analytics dashboard.
    -->
</body>
</html>
