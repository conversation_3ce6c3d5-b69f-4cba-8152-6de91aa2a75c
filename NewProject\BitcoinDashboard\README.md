# Bitcoin Dashboard App

A desktop application for tracking and visualizing organizations with Bitcoin on their balance sheets.

## Overview

This application provides a dashboard to monitor companies that hold Bitcoin as part of their treasury strategy. It displays key metrics, visualizations, and detailed information about these companies.

## Features
- Track companies with Bitcoin holdings
- Visualize adoption trends
- Compare holdings between companies
- Analyze industry breakdown
- View historical growth of corporate Bitcoin holdings

## Technology
- Built with Python programming language
- Modern Bitcoin-themed UI (Orange, Black, Grey, White)
- Tkinter for desktop UI framework
- Matplotlib for data visualization
- Structured data models for Bitcoin holdings information

## Running the Application

### Prerequisites

- Python 3.6+ installed
- Required Python packages:
  - tkinter (usually comes with Python)
  - matplotlib
  - numpy

### Installation

1. Navigate to the project directory:
   ```
   cd NewProject/BitcoinDashboard
   ```

2. Install required packages:
   ```
   pip install matplotlib numpy
   ```

3. Run the application:
   ```
   python main.py
   ```

## Project Structure

- `main.py` - Application entry point and main implementation
- `sample_data.json` - Sample data for Bitcoin holdings

## Future Enhancements

- Interactive navigation between different views
- Real-time data from financial APIs
- Advanced visualizations and charts
- Filtering and sorting capabilities
- Export functionality for reports

## About PhoenixBit

PhoenixBit is an open source Bitcoin company committed to advancing decentralized technologies on the Bitcoin standard. As a decentralized organization, we prioritize transparency, community governance, and adherence to Bitcoin's core principles.
