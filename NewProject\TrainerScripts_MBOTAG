<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>TrainerScripts - Communication Mastery for Fitness Pros</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 0;
      background-color: #f9f9f9;
      color: #333;
    }

    header {
      background: linear-gradient(to right, #2c3e50, #3498db);
      color: white;
      text-align: center;
      padding: 3rem 1rem;
    }

    header h1 {
      font-size: 2.5rem;
      margin-bottom: 0.5rem;
    }

    header p {
      font-size: 1.2rem;
      max-width: 700px;
      margin: 0 auto;
    }

    .container {
      max-width: 1000px;
      margin: auto;
      padding: 2rem 1rem;
    }

    section {
      margin-bottom: 3rem;
    }

    h2 {
      color: #2c3e50;
    }

    ul {
      list-style-type: none;
      padding-left: 0;
    }

    ul li::before {
      content: "✅ ";
      color: #2ecc71;
    }

    .script-box {
      background: white;
      border-left: 5px solid #3498db;
      padding: 1rem;
      margin-bottom: 1rem;
      box-shadow: 0 2px 5px rgba(0,0,0,0.05);
      display: none;
    }

    .script-title {
      cursor: pointer;
      padding: 1rem;
      background-color: #ecf0f1;
      border-radius: 5px;
      font-weight: bold;
      transition: background 0.3s;
    }

    .script-title:hover {
      background-color: #dce3e6;
    }

    .download-section {
      background: #ecf0f1;
      padding: 2rem;
      text-align: center;
      border-radius: 8px;
    }

    .download-section h3 {
      margin-bottom: 1rem;
    }

    .email-form input[type="email"] {
      padding: 0.7rem;
      width: 80%;
      max-width: 300px;
      border: 1px solid #ccc;
      border-radius: 4px;
      margin-right: 0.5rem;
    }

    .email-form button {
      padding: 0.7rem 1.5rem;
      background-color: #27ae60;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    .testimonial {
      font-style: italic;
      margin-top: 1rem;
      color: #555;
    }

    footer {
      text-align: center;
      padding: 1rem;
      background: #2c3e50;
      color: white;
    }

    @media (max-width: 600px) {
      .email-form input[type="email"],
      .email-form button {
        width: 100%;
        margin-bottom: 0.5rem;
      }
    }
  </style>
</head>
<body>

  <header>
    <h1>Fitness Communication Mastery</h1>
    <p>Transform your client relationships with 15 proven communication scripts designed specifically for fitness professionals.</p>
  </header>

  <div class="container">

    <section>
      <h2>What’s Inside the Toolkit?</h2>
      <ul>
        <li><strong>15 Professional Scripts</strong>: For every stage of the client journey</li>
        <li><strong>Customizable Frameworks</strong>: Use them in emails, calls, or sessions</li>
        <li><strong>Client-Centered Language</strong>: Build trust and clarity</li>
        <li><strong>Action-Oriented Solutions</strong>: Keep clients moving forward</li>
      </ul>
    </section>

    <section>
      <h2>Preview All 15 Scripts</h2>

      <!-- Script 1 -->
      <div class="script-title" onclick="toggleScript('script1')">1. Breaking Through Fitness Burnout</div>
      <div id="script1" class="script-box">
        <strong>When to use:</strong> When a long-term client is losing motivation or showing signs of burnout.<br><br>
        <strong>Script:</strong><br>
        "[Client Name], I've noticed you've been feeling less enthusiastic about your training lately. That's completely normal after putting in so much effort over the past [time period]. Let's take a moment to acknowledge how far you've come before we talk about moving forward. Would you be open to exploring some ways we could reignite your enthusiasm while still working toward your goals?"
        <br><br>
        <strong>Key Elements:</strong>
        <ul>
          <li>Acknowledges effort and progress</li>
          <li>Normalizes burnout as part of the journey</li>
          <li>Offers collaborative problem-solving</li>
          <li>Maintains focus on long-term goals</li>
        </ul>
      </div>

      <!-- Script 2 -->
      <div class="script-title" onclick="toggleScript('script2')">2. Setting Realistic Expectations During Onboarding</div>
      <div id="script2" class="script-box">
        <strong>When to use:</strong> First session with a new client.<br><br>
        <strong>Script:</strong><br>
        "Before we dive into your program, let's make sure we're aligned on what success looks like and how we'll get there..."
        <br><br>
        *(Full script available in downloadable toolkit)*
      </div>

      <!-- Repeat for other scripts -->
      <!-- You can add more script previews here -->

    </section>

    <section class="download-section">
      <h3>Download the Full Toolkit</h3>
      <p>Leave your email below to receive instant access to all 15 communication scripts + bonus guide.</p>
      <form class="email-form">
        <input type="email" placeholder="Your Email" required />
        <button type="submit">Get Free Access</button>
      </form>
      <small>We respect your privacy. Unsubscribe at any time.</small>
    </section>

    <section>
      <h2>Testimonials</h2>
      <blockquote class="testimonial">
        "These scripts have completely changed how I interact with my clients. I feel more confident and prepared for any conversation." – Sarah M., Personal Trainer
      </blockquote>
      <blockquote class="testimonial">
        "From re-engaging lapsed clients to handling pricing conversations, this toolkit is a game-changer." – James T., Online Coach
      </blockquote>
    </section>

  </div>

  <footer>
    &copy; 2025 Fitness Communication Mastery | Created for passionate fitness pros everywhere.
  </footer>

  <script>
    function toggleScript(id) {
      const scriptBox = document.getElementById(id);
      scriptBox.style.display = scriptBox.style.display === 'block' ? 'none' : 'block';
    }
  </script>

</body>
</html>