# Running the Bitcoin Dashboard on Localhost

This document provides instructions for running the Bitcoin Dashboard on a local web server.

## New Feature: Progress Tracking with Color-Coded Legend

The dashboard now includes a progress tracking feature with a color-coded legend:

- 🔴 **Red** - Not Started/Not Finished
- 🟡 **Yellow** - Pending/In Progress
- 🟢 **Green** - Completed

You can access this feature by clicking on the "Progress" item in the sidebar. The progress tracker allows you to:

1. View the current status of all project tasks
2. Add new tasks with their status
3. Track development progress across different categories

This feature helps maintain transparency and provides a clear visual indication of the project's status.

## Why Use a Local Server?

When opening HTML files directly from your file system (using the `file://` protocol), browsers impose certain security restrictions that can prevent some features from working correctly. Running the dashboard through a local web server (using the `http://` protocol) ensures that all features work as expected.

## Option 1: Python Server (Recommended)

If you have Python installed, this is the easiest option:

1. Open a command prompt or terminal
2. Navigate to the BitcoinDashboard directory
3. Run the `start_server.bat` file or execute:
   ```
   python server.py
   ```
4. The dashboard will automatically open in your default browser at http://localhost:8000/dashboard.html
5. Press Ctrl+C in the command prompt to stop the server when you're done

## Option 2: Node.js Server

If you have Node.js installed:

1. Open a command prompt or terminal
2. Navigate to the BitcoinDashboard directory
3. Run the `start_node_server.bat` file or execute:
   ```
   node server.js
   ```
4. The dashboard will automatically open in your default browser at http://localhost:8000/dashboard.html
5. Press Ctrl+C in the command prompt to stop the server when you're done

## Option 3: PHP Server

If you have PHP installed:

1. Open a command prompt or terminal
2. Navigate to the BitcoinDashboard directory
3. Run the `start_php_server.bat` file or execute:
   ```
   php server.php
   ```
4. The dashboard will automatically open in your default browser at http://localhost:8000/dashboard.html
5. Press Ctrl+C in the command prompt to stop the server when you're done

## Option 4: Using VS Code Live Server Extension

If you're using Visual Studio Code:

1. Install the "Live Server" extension by Ritwick Dey
2. Right-click on the `dashboard.html` file in VS Code
3. Select "Open with Live Server"
4. The dashboard will open in your default browser

## Option 5: Using XAMPP, WAMP, or MAMP

If you have XAMPP, WAMP, or MAMP installed:

1. Start your local server
2. Copy the BitcoinDashboard directory to your web server's document root (e.g., `htdocs` for XAMPP)
3. Open your browser and navigate to http://localhost/BitcoinDashboard/dashboard.html

## Troubleshooting

If you encounter issues:

1. **Check your browser console for errors**: Press F12 to open developer tools, then go to the Console tab
2. **Try a different browser**: Some browsers have stricter security policies for local files
3. **Check if the port is already in use**: If port 8000 is already in use, edit the server file to use a different port
4. **Check your firewall settings**: Make sure your firewall isn't blocking the local server

## Need More Help?

If you're still having issues, please check if you have the necessary software installed:

- Python: Run `python --version` in a command prompt
- Node.js: Run `node --version` in a command prompt
- PHP: Run `php --version` in a command prompt

If none of these are installed, consider installing Python as it's the most versatile option.
