<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Dashboard - PhoenixBit</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #121212;
            color: #FFFFFF;
            margin: 0;
            padding: 0;
        }

        .header {
            background-color: #F7931A;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            margin: 0;
            color: white;
        }

        .container {
            display: flex;
            min-height: calc(100vh - 80px);
        }

        .sidebar {
            width: 200px;
            background-color: #1E1E1E;
            padding: 20px;
        }

        .sidebar-item {
            padding: 10px;
            margin-bottom: 5px;
            cursor: pointer;
            border-radius: 5px;
        }

        .sidebar-item:hover, .sidebar-item.active {
            background-color: #F7931A;
        }

        .main-content {
            flex: 1;
            padding: 20px;
        }

        .metrics {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background-color: #2D2D2D;
            padding: 20px;
            border-radius: 10px;
            flex: 1;
        }

        .metric-title {
            font-size: 14px;
            color: #CCCCCC;
            margin-bottom: 10px;
        }

        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #F7931A;
        }

        .charts {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }

        .chart {
            background-color: #2D2D2D;
            padding: 20px;
            border-radius: 10px;
            flex: 1;
        }

        .chart-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #444;
        }

        th {
            background-color: #1E1E1E;
            color: #F7931A;
        }

        tr:hover {
            background-color: #2D2D2D;
        }

        .bitcoin-value {
            color: #F7931A;
            font-weight: bold;
        }

        .bar-chart {
            margin-top: 20px;
        }

        .bar-item {
            margin-bottom: 10px;
        }

        .bar-label {
            display: inline-block;
            width: 150px;
        }

        .bar {
            display: inline-block;
            height: 20px;
            background-color: #F7931A;
            margin-right: 10px;
            vertical-align: middle;
        }

        .bar-value {
            display: inline-block;
            vertical-align: middle;
        }

        /* Progress tracking styles */
        .legend {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .legend-item {
            display: flex;
            align-items: center;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
        }

        .legend-red {
            background-color: #E74C3C;
        }

        .legend-yellow {
            background-color: #F1C40F;
        }

        .legend-green {
            background-color: #2ECC71;
        }

        .progress-section {
            margin-bottom: 30px;
        }

        .progress-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
            background-color: #2D2D2D;
        }

        .progress-status {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .progress-red {
            background-color: #E74C3C;
        }

        .progress-yellow {
            background-color: #F1C40F;
        }

        .progress-green {
            background-color: #2ECC71;
        }

        .progress-text {
            flex-grow: 1;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BITCOIN DASHBOARD</h1>
        <div>Last Updated: <span id="current-date"></span></div>
    </div>

    <div class="container">
        <div class="sidebar">
            <div class="sidebar-item active">Dashboard</div>
            <div class="sidebar-item">Companies</div>
            <div class="sidebar-item">Analysis</div>
            <div class="sidebar-item">Progress</div>
            <div class="sidebar-item">Settings</div>
        </div>

        <div class="main-content">
            <h2>DASHBOARD OVERVIEW</h2>

            <div class="metrics">
                <div class="metric-card">
                    <div class="metric-title">Total Bitcoin on Balance Sheets</div>
                    <div class="metric-value">1,456,789 BTC</div>
                </div>

                <div class="metric-card">
                    <div class="metric-title">Number of Companies</div>
                    <div class="metric-value">42</div>
                </div>

                <div class="metric-card">
                    <div class="metric-title">% of Bitcoin Supply</div>
                    <div class="metric-value">6.9%</div>
                </div>
            </div>

            <div class="charts">
                <div class="chart">
                    <div class="chart-title">Top Companies by Bitcoin Holdings</div>
                    <table>
                        <thead>
                            <tr>
                                <th>Company</th>
                                <th>Ticker</th>
                                <th>BTC Holdings</th>
                                <th>% Treasury</th>
                                <th>First Purchase</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>MicroStrategy</td>
                                <td>MSTR</td>
                                <td class="bitcoin-value">140,000 BTC</td>
                                <td>95%</td>
                                <td>2020-08-11</td>
                            </tr>
                            <tr>
                                <td>Tesla</td>
                                <td>TSLA</td>
                                <td class="bitcoin-value">42,000 BTC</td>
                                <td>15%</td>
                                <td>2021-02-08</td>
                            </tr>
                            <tr>
                                <td>Galaxy Digital</td>
                                <td>GLXY</td>
                                <td class="bitcoin-value">16,400 BTC</td>
                                <td>70%</td>
                                <td>2018-11-05</td>
                            </tr>
                            <tr>
                                <td>Marathon Digital</td>
                                <td>MARA</td>
                                <td class="bitcoin-value">13,380 BTC</td>
                                <td>80%</td>
                                <td>2021-01-15</td>
                            </tr>
                            <tr>
                                <td>Voyager Digital</td>
                                <td>VOYG</td>
                                <td class="bitcoin-value">12,260 BTC</td>
                                <td>60%</td>
                                <td>2021-01-12</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="charts">
                <div class="chart">
                    <div class="chart-title">Industry Breakdown</div>
                    <div class="bar-chart">
                        <div class="bar-item">
                            <span class="bar-label">Software</span>
                            <span class="bar" style="width: 160px;"></span>
                            <span class="bar-value">32%</span>
                        </div>
                        <div class="bar-item">
                            <span class="bar-label">Financial Technology</span>
                            <span class="bar" style="width: 140px;"></span>
                            <span class="bar-value">28%</span>
                        </div>
                        <div class="bar-item">
                            <span class="bar-label">Mining</span>
                            <span class="bar" style="width: 75px;"></span>
                            <span class="bar-value">15%</span>
                        </div>
                        <div class="bar-item">
                            <span class="bar-label">Automotive</span>
                            <span class="bar" style="width: 40px;"></span>
                            <span class="bar-value">8%</span>
                        </div>
                        <div class="bar-item">
                            <span class="bar-label">Gaming</span>
                            <span class="bar" style="width: 25px;"></span>
                            <span class="bar-value">5%</span>
                        </div>
                        <div class="bar-item">
                            <span class="bar-label">Other</span>
                            <span class="bar" style="width: 60px;"></span>
                            <span class="bar-value">12%</span>
                        </div>
                    </div>
                </div>

                <div class="chart">
                    <div class="chart-title">Historical Bitcoin Holdings Growth</div>
                    <div class="bar-chart">
                        <div class="bar-item">
                            <span class="bar-label">2020-08</span>
                            <span class="bar" style="width: 29px;"></span>
                            <span class="bar-value">85,000 BTC</span>
                        </div>
                        <div class="bar-item">
                            <span class="bar-label">2020-12</span>
                            <span class="bar" style="width: 77px;"></span>
                            <span class="bar-value">225,000 BTC</span>
                        </div>
                        <div class="bar-item">
                            <span class="bar-label">2021-03</span>
                            <span class="bar" style="width: 280px;"></span>
                            <span class="bar-value">815,000 BTC</span>
                        </div>
                        <div class="bar-item">
                            <span class="bar-label">2021-06</span>
                            <span class="bar" style="width: 384px;"></span>
                            <span class="bar-value">1,120,000 BTC</span>
                        </div>
                        <div class="bar-item">
                            <span class="bar-label">2021-12</span>
                            <span class="bar" style="width: 453px;"></span>
                            <span class="bar-value">1,320,000 BTC</span>
                        </div>
                        <div class="bar-item">
                            <span class="bar-label">2022-06</span>
                            <span class="bar" style="width: 473px;"></span>
                            <span class="bar-value">1,380,000 BTC</span>
                        </div>
                        <div class="bar-item">
                            <span class="bar-label">2022-12</span>
                            <span class="bar" style="width: 484px;"></span>
                            <span class="bar-value">1,410,000 BTC</span>
                        </div>
                        <div class="bar-item">
                            <span class="bar-label">2023-06</span>
                            <span class="bar" style="width: 500px;"></span>
                            <span class="bar-value">1,456,789 BTC</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Tracking View (hidden by default) -->
    <div id="progress-view" class="main-content" style="display: none;">
        <h2>PROJECT PROGRESS TRACKER</h2>

        <div class="legend">
            <div class="legend-item">
                <div class="legend-color legend-red"></div>
                <div>Not Started/Not Finished</div>
            </div>
            <div class="legend-item">
                <div class="legend-color legend-yellow"></div>
                <div>Pending/In Progress</div>
            </div>
            <div class="legend-item">
                <div class="legend-color legend-green"></div>
                <div>Completed</div>
            </div>
        </div>

        <div class="progress-section">
            <h3>Dashboard Development</h3>
            <div class="progress-item">
                <div class="progress-status progress-green"></div>
                <div class="progress-text">Create HTML structure</div>
            </div>
            <div class="progress-item">
                <div class="progress-status progress-green"></div>
                <div class="progress-text">Implement Bitcoin theme</div>
            </div>
            <div class="progress-item">
                <div class="progress-status progress-green"></div>
                <div class="progress-text">Create responsive layout</div>
            </div>
            <div class="progress-item">
                <div class="progress-status progress-green"></div>
                <div class="progress-text">Implement sidebar navigation</div>
            </div>
        </div>

        <div class="progress-section">
            <h3>Data Management</h3>
            <div class="progress-item">
                <div class="progress-status progress-green"></div>
                <div class="progress-text">Create sample data</div>
            </div>
            <div class="progress-item">
                <div class="progress-status progress-green"></div>
                <div class="progress-text">Implement data loading</div>
            </div>
            <div class="progress-item">
                <div class="progress-status progress-yellow"></div>
                <div class="progress-text">Connect to external APIs</div>
            </div>
            <div class="progress-item">
                <div class="progress-status progress-red"></div>
                <div class="progress-text">Implement data persistence</div>
            </div>
        </div>

        <div class="progress-section">
            <h3>Visualizations</h3>
            <div class="progress-item">
                <div class="progress-status progress-green"></div>
                <div class="progress-text">Implement metrics cards</div>
            </div>
            <div class="progress-item">
                <div class="progress-status progress-green"></div>
                <div class="progress-text">Create companies table</div>
            </div>
            <div class="progress-item">
                <div class="progress-status progress-green"></div>
                <div class="progress-text">Implement bar charts</div>
            </div>
            <div class="progress-item">
                <div class="progress-status progress-yellow"></div>
                <div class="progress-text">Add interactive charts</div>
            </div>
            <div class="progress-item">
                <div class="progress-status progress-red"></div>
                <div class="progress-text">Implement geographic map</div>
            </div>
        </div>

        <div class="progress-section">
            <h3>Server Implementation</h3>
            <div class="progress-item">
                <div class="progress-status progress-green"></div>
                <div class="progress-text">Create Python HTTP server</div>
            </div>
            <div class="progress-item">
                <div class="progress-status progress-green"></div>
                <div class="progress-text">Create Node.js server alternative</div>
            </div>
            <div class="progress-item">
                <div class="progress-status progress-green"></div>
                <div class="progress-text">Create PHP server alternative</div>
            </div>
            <div class="progress-item">
                <div class="progress-status progress-yellow"></div>
                <div class="progress-text">Add API endpoints</div>
            </div>
        </div>

        <div class="progress-section">
            <h3>Add New Task</h3>
            <div style="background-color: #2D2D2D; padding: 20px; border-radius: 10px;">
                <div style="margin-bottom: 15px;">
                    <label for="task-section" style="display: block; margin-bottom: 5px;">Section:</label>
                    <select id="task-section" style="width: 100%; padding: 8px; background-color: #1E1E1E; color: white; border: 1px solid #444; border-radius: 5px;">
                        <option value="Dashboard Development">Dashboard Development</option>
                        <option value="Data Management">Data Management</option>
                        <option value="Visualizations">Visualizations</option>
                        <option value="Server Implementation">Server Implementation</option>
                        <option value="Documentation">Documentation</option>
                        <option value="Testing">Testing</option>
                    </select>
                </div>

                <div style="margin-bottom: 15px;">
                    <label for="task-name" style="display: block; margin-bottom: 5px;">Task Name:</label>
                    <input type="text" id="task-name" style="width: 100%; padding: 8px; background-color: #1E1E1E; color: white; border: 1px solid #444; border-radius: 5px;">
                </div>

                <div style="margin-bottom: 15px;">
                    <label for="task-status" style="display: block; margin-bottom: 5px;">Status:</label>
                    <select id="task-status" style="width: 100%; padding: 8px; background-color: #1E1E1E; color: white; border: 1px solid #444; border-radius: 5px;">
                        <option value="red">Not Started/Not Finished</option>
                        <option value="yellow">Pending/In Progress</option>
                        <option value="green">Completed</option>
                    </select>
                </div>

                <button id="add-task-btn" style="background-color: #F7931A; color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; font-weight: bold;">Add Task</button>
            </div>
        </div>
    </div>

    <script>
        // Set current date
        document.getElementById('current-date').textContent = new Date().toLocaleString();

        // Add click handlers for sidebar items
        const sidebarItems = document.querySelectorAll('.sidebar-item');
        const mainContent = document.querySelector('.main-content');
        const progressView = document.getElementById('progress-view');

        sidebarItems.forEach(item => {
            item.addEventListener('click', function() {
                sidebarItems.forEach(i => i.classList.remove('active'));
                this.classList.add('active');

                // Handle view switching
                const viewName = this.textContent;

                if (viewName === 'Progress') {
                    mainContent.style.display = 'none';
                    progressView.style.display = 'block';
                } else if (viewName === 'Dashboard') {
                    mainContent.style.display = 'block';
                    progressView.style.display = 'none';
                } else {
                    mainContent.style.display = 'block';
                    progressView.style.display = 'none';
                    alert('Switching to ' + viewName + ' view');
                }
            });
        });

        // Add task functionality
        document.getElementById('add-task-btn').addEventListener('click', function() {
            const section = document.getElementById('task-section').value;
            const taskName = document.getElementById('task-name').value;
            const status = document.getElementById('task-status').value;

            if (!taskName) {
                alert('Please enter a task name');
                return;
            }

            // Find the section to add the task to
            const sections = document.querySelectorAll('.progress-section h3');
            let targetSection = null;

            for (const sectionHeader of sections) {
                if (sectionHeader.textContent === section) {
                    targetSection = sectionHeader.parentElement;
                    break;
                }
            }

            if (!targetSection) {
                alert('Section not found');
                return;
            }

            // Create new task element
            const taskItem = document.createElement('div');
            taskItem.className = 'progress-item';

            const statusElement = document.createElement('div');
            statusElement.className = `progress-status progress-${status}`;

            const textElement = document.createElement('div');
            textElement.className = 'progress-text';
            textElement.textContent = taskName;

            taskItem.appendChild(statusElement);
            taskItem.appendChild(textElement);

            // Add the task before the "Add New Task" section
            targetSection.insertBefore(taskItem, targetSection.lastElementChild);

            // Clear the form
            document.getElementById('task-name').value = '';

            // Show confirmation
            alert(`Task "${taskName}" added to ${section} with ${status} status`);
        });
    </script>
</body>
</html>
