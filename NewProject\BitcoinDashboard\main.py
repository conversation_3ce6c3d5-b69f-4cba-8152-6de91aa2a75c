#!/usr/bin/env python3
"""
Bitcoin Dashboard - PhoenixBit
A desktop application for tracking and visualizing organizations with Bitcoin on their balance sheets.
"""

import tkinter as tk
from tkinter import ttk
import json
import os
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib
matplotlib.use("TkAgg")

# Bitcoin theme colors
BITCOIN_ORANGE = "#F7931A"
DEEP_BLACK = "#121212"
LIGHT_GRAY = "#D3D3D3"
MEDIUM_GRAY = "#808080"
WHITE = "#FFFFFF"

class BitcoinDashboardApp:
    """Main application class for the Bitcoin Dashboard."""
    
    def __init__(self, root):
        """Initialize the application."""
        self.root = root
        self.root.title("Bitcoin Dashboard - PhoenixBit")
        self.root.geometry("1200x800")
        self.root.configure(bg=DEEP_BLACK)
        
        # Load data
        self.load_data()
        
        # Set up the UI
        self.setup_ui()
    
    def load_data(self):
        """Load sample data from JSON file."""
        try:
            with open('sample_data.json', 'r') as file:
                self.data = json.load(file)
        except FileNotFoundError:
            # Use default data if file not found
            self.data = {
                "companies": [
                    {
                        "name": "MicroStrategy",
                        "ticker": "MSTR",
                        "bitcoin_holdings": 140000,
                        "treasury_percentage": 95,
                        "first_purchase_date": "2020-08-11",
                        "industry": "Software"
                    },
                    {
                        "name": "Tesla",
                        "ticker": "TSLA",
                        "bitcoin_holdings": 42000,
                        "treasury_percentage": 15,
                        "first_purchase_date": "2021-02-08",
                        "industry": "Automotive"
                    },
                    {
                        "name": "Galaxy Digital",
                        "ticker": "GLXY",
                        "bitcoin_holdings": 16400,
                        "treasury_percentage": 70,
                        "first_purchase_date": "2018-11-05",
                        "industry": "Financial Technology"
                    }
                ],
                "total_bitcoin": 1456789,
                "total_companies": 42,
                "bitcoin_supply_percentage": 6.9,
                "industry_breakdown": {
                    "Software": 32,
                    "Financial Technology": 28,
                    "Mining": 15,
                    "Automotive": 8,
                    "Other": 17
                },
                "historical_total": [
                    {"date": "2020-08", "amount": 85000},
                    {"date": "2020-12", "amount": 225000},
                    {"date": "2021-03", "amount": 815000},
                    {"date": "2021-06", "amount": 1120000},
                    {"date": "2021-12", "amount": 1320000},
                    {"date": "2022-06", "amount": 1380000},
                    {"date": "2022-12", "amount": 1410000},
                    {"date": "2023-06", "amount": 1456789}
                ]
            }
    
    def setup_ui(self):
        """Set up the user interface."""
        # Create main frame
        self.main_frame = tk.Frame(self.root, bg=DEEP_BLACK)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Create header
        self.create_header()
        
        # Create content area with sidebar and main content
        self.content_frame = tk.Frame(self.main_frame, bg=DEEP_BLACK)
        self.content_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Create sidebar
        self.create_sidebar()
        
        # Create main content area
        self.main_content = tk.Frame(self.content_frame, bg=DEEP_BLACK)
        self.main_content.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10)
        
        # Show dashboard by default
        self.show_dashboard()
    
    def create_header(self):
        """Create the application header."""
        header_frame = tk.Frame(self.main_frame, bg=BITCOIN_ORANGE, height=60)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Logo/Title
        title_label = tk.Label(
            header_frame, 
            text="BITCOIN DASHBOARD", 
            font=("Arial", 18, "bold"), 
            bg=BITCOIN_ORANGE, 
            fg=WHITE
        )
        title_label.pack(side=tk.LEFT, padx=20, pady=10)
        
        # Current date
        date_label = tk.Label(
            header_frame, 
            text=f"Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            font=("Arial", 10),
            bg=BITCOIN_ORANGE,
            fg=WHITE
        )
        date_label.pack(side=tk.RIGHT, padx=20, pady=10)
    
    def create_sidebar(self):
        """Create the sidebar navigation."""
        sidebar = tk.Frame(self.content_frame, bg=DEEP_BLACK, width=200)
        sidebar.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # Ensure the sidebar maintains its width
        sidebar.pack_propagate(False)
        
        # Navigation buttons
        nav_buttons = [
            ("Dashboard", self.show_dashboard),
            ("Companies", self.show_companies),
            ("Analysis", self.show_analysis),
            ("Settings", self.show_settings)
        ]
        
        for text, command in nav_buttons:
            btn = tk.Button(
                sidebar,
                text=text,
                font=("Arial", 12),
                bg=DEEP_BLACK,
                fg=WHITE,
                activebackground=BITCOIN_ORANGE,
                activeforeground=WHITE,
                bd=0,
                padx=10,
                pady=10,
                anchor="w",
                width=20,
                command=command
            )
            btn.pack(fill=tk.X, pady=2)
    
    def clear_main_content(self):
        """Clear the main content area."""
        for widget in self.main_content.winfo_children():
            widget.destroy()
    
    def show_dashboard(self):
        """Display the main dashboard."""
        self.clear_main_content()
        
        # Create dashboard title
        title = tk.Label(
            self.main_content,
            text="DASHBOARD OVERVIEW",
            font=("Arial", 16, "bold"),
            bg=DEEP_BLACK,
            fg=WHITE
        )
        title.pack(anchor="w", pady=(0, 20))
        
        # Create metrics cards
        metrics_frame = tk.Frame(self.main_content, bg=DEEP_BLACK)
        metrics_frame.pack(fill=tk.X, pady=10)
        
        # Total Bitcoin card
        total_btc_frame = tk.Frame(metrics_frame, bg=MEDIUM_GRAY, padx=15, pady=15, bd=0)
        total_btc_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        tk.Label(
            total_btc_frame,
            text="Total Bitcoin on Balance Sheets",
            font=("Arial", 12),
            bg=MEDIUM_GRAY,
            fg=WHITE
        ).pack(anchor="w")
        
        tk.Label(
            total_btc_frame,
            text=f"{self.data['total_bitcoin']:,} BTC",
            font=("Arial", 18, "bold"),
            bg=MEDIUM_GRAY,
            fg=BITCOIN_ORANGE
        ).pack(anchor="w", pady=5)
        
        # Companies count card
        companies_frame = tk.Frame(metrics_frame, bg=MEDIUM_GRAY, padx=15, pady=15, bd=0)
        companies_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        tk.Label(
            companies_frame,
            text="Number of Companies",
            font=("Arial", 12),
            bg=MEDIUM_GRAY,
            fg=WHITE
        ).pack(anchor="w")
        
        tk.Label(
            companies_frame,
            text=str(self.data['total_companies']),
            font=("Arial", 18, "bold"),
            bg=MEDIUM_GRAY,
            fg=BITCOIN_ORANGE
        ).pack(anchor="w", pady=5)
        
        # Supply percentage card
        supply_frame = tk.Frame(metrics_frame, bg=MEDIUM_GRAY, padx=15, pady=15, bd=0)
        supply_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        tk.Label(
            supply_frame,
            text="% of Bitcoin Supply",
            font=("Arial", 12),
            bg=MEDIUM_GRAY,
            fg=WHITE
        ).pack(anchor="w")
        
        tk.Label(
            supply_frame,
            text=f"{self.data['bitcoin_supply_percentage']}%",
            font=("Arial", 18, "bold"),
            bg=MEDIUM_GRAY,
            fg=BITCOIN_ORANGE
        ).pack(anchor="w", pady=5)
        
        # Create charts
        charts_frame = tk.Frame(self.main_content, bg=DEEP_BLACK)
        charts_frame.pack(fill=tk.BOTH, expand=True, pady=20)
        
        # Historical chart
        self.create_historical_chart(charts_frame)
        
        # Industry breakdown chart
        self.create_industry_chart(charts_frame)
    
    def create_historical_chart(self, parent_frame):
        """Create historical Bitcoin holdings chart."""
        chart_frame = tk.Frame(parent_frame, bg=DEEP_BLACK)
        chart_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        tk.Label(
            chart_frame,
            text="Historical Bitcoin Holdings",
            font=("Arial", 14, "bold"),
            bg=DEEP_BLACK,
            fg=WHITE
        ).pack(anchor="w", pady=(0, 10))
        
        # Create matplotlib figure
        fig, ax = plt.subplots(figsize=(6, 4), facecolor=DEEP_BLACK)
        
        # Extract data
        dates = [entry['date'] for entry in self.data['historical_total']]
        amounts = [entry['amount'] for entry in self.data['historical_total']]
        
        # Plot data
        ax.plot(dates, amounts, marker='o', color=BITCOIN_ORANGE, linewidth=3)
        ax.set_facecolor(DEEP_BLACK)
        ax.tick_params(colors=WHITE)
        ax.spines['bottom'].set_color(MEDIUM_GRAY)
        ax.spines['top'].set_color(DEEP_BLACK)
        ax.spines['right'].set_color(DEEP_BLACK)
        ax.spines['left'].set_color(MEDIUM_GRAY)
        ax.set_title('Bitcoin Holdings Growth', color=WHITE)
        ax.set_ylabel('BTC', color=WHITE)
        
        # Create canvas
        canvas = FigureCanvasTkAgg(fig, master=chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def create_industry_chart(self, parent_frame):
        """Create industry breakdown pie chart."""
        chart_frame = tk.Frame(parent_frame, bg=DEEP_BLACK)
        chart_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        tk.Label(
            chart_frame,
            text="Industry Breakdown",
            font=("Arial", 14, "bold"),
            bg=DEEP_BLACK,
            fg=WHITE
        ).pack(anchor="w", pady=(0, 10))
        
        # Create matplotlib figure
        fig, ax = plt.subplots(figsize=(6, 4), facecolor=DEEP_BLACK)
        
        # Extract data
        industries = list(self.data['industry_breakdown'].keys())
        percentages = list(self.data['industry_breakdown'].values())
        
        # Custom colors with Bitcoin orange as the first color
        colors = [BITCOIN_ORANGE, '#3498db', '#2ecc71', '#e74c3c', '#9b59b6']
        
        # Plot data
        ax.pie(
            percentages, 
            labels=industries, 
            autopct='%1.1f%%', 
            startangle=90, 
            colors=colors,
            textprops={'color': WHITE}
        )
        ax.set_facecolor(DEEP_BLACK)
        ax.set_title('Industry Distribution', color=WHITE)
        
        # Create canvas
        canvas = FigureCanvasTkAgg(fig, master=chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def show_companies(self):
        """Display the companies list view."""
        self.clear_main_content()
        
        # Create companies title
        title = tk.Label(
            self.main_content,
            text="COMPANIES WITH BITCOIN",
            font=("Arial", 16, "bold"),
            bg=DEEP_BLACK,
            fg=WHITE
        )
        title.pack(anchor="w", pady=(0, 20))
        
        # Create table frame
        table_frame = tk.Frame(self.main_content, bg=DEEP_BLACK)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create treeview for companies
        columns = ("name", "ticker", "holdings", "percentage", "date", "industry")
        tree = ttk.Treeview(table_frame, columns=columns, show="headings")
        
        # Define headings
        tree.heading("name", text="Company")
        tree.heading("ticker", text="Ticker")
        tree.heading("holdings", text="BTC Holdings")
        tree.heading("percentage", text="% of Treasury")
        tree.heading("date", text="First Purchase")
        tree.heading("industry", text="Industry")
        
        # Define columns
        tree.column("name", width=150)
        tree.column("ticker", width=80)
        tree.column("holdings", width=120)
        tree.column("percentage", width=120)
        tree.column("date", width=120)
        tree.column("industry", width=150)
        
        # Insert data
        for company in self.data["companies"]:
            tree.insert(
                "", 
                "end", 
                values=(
                    company["name"],
                    company["ticker"],
                    f"{company['bitcoin_holdings']:,} BTC",
                    f"{company['treasury_percentage']}%",
                    company["first_purchase_date"],
                    company["industry"]
                )
            )
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        tree.pack(fill=tk.BOTH, expand=True)
        
        # Configure style for dark theme
        style = ttk.Style()
        style.configure("Treeview", 
                        background=DEEP_BLACK, 
                        foreground=WHITE, 
                        fieldbackground=DEEP_BLACK)
        style.map('Treeview', background=[('selected', BITCOIN_ORANGE)])
        
        # Create holdings chart
        self.create_holdings_chart()
    
    def create_holdings_chart(self):
        """Create chart showing top companies by holdings."""
        chart_frame = tk.Frame(self.main_content, bg=DEEP_BLACK, height=300)
        chart_frame.pack(fill=tk.X, pady=20)
        
        tk.Label(
            chart_frame,
            text="Top Companies by Bitcoin Holdings",
            font=("Arial", 14, "bold"),
            bg=DEEP_BLACK,
            fg=WHITE
        ).pack(anchor="w", pady=(0, 10))
        
        # Create matplotlib figure
        fig, ax = plt.subplots(figsize=(10, 4), facecolor=DEEP_BLACK)
        
        # Sort companies by holdings and get top 5
        sorted_companies = sorted(
            self.data["companies"], 
            key=lambda x: x["bitcoin_holdings"], 
            reverse=True
        )[:5]
        
        # Extract data
        names = [company["name"] for company in sorted_companies]
        holdings = [company["bitcoin_holdings"] for company in sorted_companies]
        
        # Plot data
        bars = ax.barh(names, holdings, color=BITCOIN_ORANGE)
        ax.set_facecolor(DEEP_BLACK)
        ax.tick_params(colors=WHITE)
        ax.spines['bottom'].set_color(MEDIUM_GRAY)
        ax.spines['top'].set_color(DEEP_BLACK)
        ax.spines['right'].set_color(DEEP_BLACK)
        ax.spines['left'].set_color(MEDIUM_GRAY)
        ax.set_title('Top Companies by Bitcoin Holdings', color=WHITE)
        ax.set_xlabel('BTC', color=WHITE)
        
        # Add value labels
        for bar in bars:
            width = bar.get_width()
            ax.text(
                width + 1000,
                bar.get_y() + bar.get_height()/2,
                f'{int(width):,} BTC',
                ha='left',
                va='center',
                color=WHITE
            )
        
        # Create canvas
        canvas = FigureCanvasTkAgg(fig, master=chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def show_analysis(self):
        """Display the analysis view."""
        self.clear_main_content()
        
        # Create analysis title
        title = tk.Label(
            self.main_content,
            text="BITCOIN ADOPTION ANALYSIS",
            font=("Arial", 16, "bold"),
            bg=DEEP_BLACK,
            fg=WHITE
        )
        title.pack(anchor="w", pady=(0, 20))
        
        # Placeholder for analysis content
        analysis_frame = tk.Frame(self.main_content, bg=MEDIUM_GRAY, padx=20, pady=20)
        analysis_frame.pack(fill=tk.BOTH, expand=True)
        
        tk.Label(
            analysis_frame,
            text="Analysis features coming soon!",
            font=("Arial", 14),
            bg=MEDIUM_GRAY,
            fg=WHITE
        ).pack(pady=100)
    
    def show_settings(self):
        """Display the settings view."""
        self.clear_main_content()
        
        # Create settings title
        title = tk.Label(
            self.main_content,
            text="SETTINGS",
            font=("Arial", 16, "bold"),
            bg=DEEP_BLACK,
            fg=WHITE
        )
        title.pack(anchor="w", pady=(0, 20))
        
        # Settings content
        settings_frame = tk.Frame(self.main_content, bg=DEEP_BLACK)
        settings_frame.pack(fill=tk.BOTH, expand=True)
        
        # Theme settings
        theme_frame = tk.LabelFrame(
            settings_frame,
            text="Theme Settings",
            font=("Arial", 12),
            bg=DEEP_BLACK,
            fg=WHITE,
            padx=10,
            pady=10
        )
        theme_frame.pack(fill=tk.X, pady=10)
        
        # Data settings
        data_frame = tk.LabelFrame(
            settings_frame,
            text="Data Settings",
            font=("Arial", 12),
            bg=DEEP_BLACK,
            fg=WHITE,
            padx=10,
            pady=10
        )
        data_frame.pack(fill=tk.X, pady=10)
        
        # About section
        about_frame = tk.LabelFrame(
            settings_frame,
            text="About",
            font=("Arial", 12),
            bg=DEEP_BLACK,
            fg=WHITE,
            padx=10,
            pady=10
        )
        about_frame.pack(fill=tk.X, pady=10)
        
        tk.Label(
            about_frame,
            text="Bitcoin Dashboard v1.0",
            font=("Arial", 10),
            bg=DEEP_BLACK,
            fg=WHITE
        ).pack(anchor="w")
        
        tk.Label(
            about_frame,
            text="© 2023 PhoenixBit - An Open Source Bitcoin Company",
            font=("Arial", 10),
            bg=DEEP_BLACK,
            fg=WHITE
        ).pack(anchor="w")
        
        tk.Label(
            about_frame,
            text="Built on the Bitcoin Standard",
            font=("Arial", 10),
            bg=DEEP_BLACK,
            fg=BITCOIN_ORANGE
        ).pack(anchor="w")


def main():
    """Main entry point for the application."""
    root = tk.Tk()
    app = BitcoinDashboardApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()
