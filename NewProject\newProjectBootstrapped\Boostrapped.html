<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>TrainerScripts - Communication Mastery</title>
  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 text-gray-800 font-sans">

<!-- Header -->
<header class="bg-gradient-to-r from-blue-600 to-indigo-700 text-white text-center py-12 px-4">
  <h1 class="text-3xl md:text-4xl font-bold">Fitness Communication Mastery</h1>
  <p class="mt-4 text-lg max-w-xl mx-auto">
    Transform your client relationships with 15 proven communication scripts designed specifically for fitness professionals.
  </p>
</header>

<main class="max-w-4xl mx-auto px-4 py-12 space-y-12">

  <!-- What's Inside -->
  <section>
    <h2 class="text-2xl font-bold mb-4 text-gray-800">What’s Inside the Toolkit?</h2>
    <ul class="space-y-2 list-disc pl-5 text-gray-700">
      <li><strong>15 Professional Scripts:</strong> For every stage of the client journey</li>
      <li><strong>Customizable Frameworks:</strong> Use them in emails, calls, or sessions</li>
      <li><strong>Client-Centered Language:</strong> Build trust and clarity</li>
      <li><strong>Action-Oriented Solutions:</strong> Keep clients moving forward</li>
    </ul>
  </section>

  <!-- Preview All 15 Scripts -->
  <section>
    <h2 class="text-2xl font-bold mb-4 text-gray-800">Preview All 15 Scripts</h2>

    <!-- Script 1 -->
    <div class="border border-gray-300 rounded-md overflow-hidden shadow-sm hover:shadow-md transition-shadow">
      <button onclick="toggleScript('script1')" class="w-full text-left px-4 py-3 bg-gray-100 font-semibold flex justify-between items-center">
        <span>1. Breaking Through Fitness Burnout</span>
        <svg id="arrow-script1" class="w-5 h-5 transition-transform transform" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
      </button>
      <div id="script1" class="hidden p-4 bg-white text-gray-700">
        <strong>When to use:</strong> When a long-term client is losing motivation or showing signs of burnout.<br><br>
        <strong>Script:</strong><br>
        "[Client Name], I've noticed you've been feeling less enthusiastic about your training lately. That's completely normal after putting in so much effort over the past [time period]. Let's take a moment to acknowledge how far you've come before we talk about moving forward. Would you be open to exploring some ways we could reignite your enthusiasm while still working toward your goals?"<br><br>
        <strong>Key Elements:</strong>
        <ul class="list-disc pl-5 mt-2 space-y-1">
          <li>Acknowledges effort and progress</li>
          <li>Normalizes burnout as part of the journey</li>
          <li>Offers collaborative problem-solving</li>
          <li>Maintains focus on long-term goals</li>
        </ul>
      </div>
    </div>

    <!-- Repeat for other scripts -->
    <!-- You can duplicate the block above for each script, changing IDs accordingly -->

    <!-- Script 2 -->
    <div class="border border-gray-300 rounded-md overflow-hidden shadow-sm hover:shadow-md transition-shadow">
      <button onclick="toggleScript('script2')" class="w-full text-left px-4 py-3 bg-gray-100 font-semibold flex justify-between items-center">
        <span>2. Setting Realistic Expectations During Onboarding</span>
        <svg id="arrow-script2" class="w-5 h-5 transition-transform transform" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
      </button>
      <div id="script2" class="hidden p-4 bg-white text-gray-700">
        <strong>When to use:</strong> First session with a new client.<br><br>
        <strong>Script:</strong><br>
        "Before we dive into your program, let's make sure we're aligned on what success looks like and how we'll get there..."
        *(Full script available in downloadable toolkit)*
      </div>
    </div>

    <!-- Add more scripts here... -->
    <!-- Duplicate the above blocks for scripts 3–15 using unique IDs -->

  </section>

  <!-- Download Section -->
  <section class="bg-gray-200 p-6 rounded-lg text-center">
    <h3 class="text-xl font-bold mb-3">Download the Full Toolkit</h3>
    <p class="mb-4">Leave your email below to receive instant access to all 15 communication scripts + bonus guide.</p>
    <form id="emailForm" class="flex flex-col sm:flex-row items-center justify-center gap-3">
      <input type="email" id="emailInput" placeholder="Your Email" required class="px-4 py-2 rounded w-full sm:w-auto flex-1 max-w-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400" />
      <button type="submit" class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded font-medium transition-colors">
        Get Free Access
      </button>
    </form>
    <small class="text-gray-500 mt-2 block">We respect your privacy. Unsubscribe at any time.</small>
  </section>

  <!-- Testimonials -->
  <section>
    <h2 class="text-2xl font-bold mb-4 text-gray-800">Testimonials</h2>
    <blockquote class="italic text-gray-600 border-l-4 border-blue-500 pl-4">
      "These scripts have completely changed how I interact with my clients. I feel more confident and prepared for any conversation." – Sarah M., Personal Trainer
    </blockquote>
    <blockquote class="italic text-gray-600 border-l-4 border-blue-500 pl-4 mt-4">
      "From re-engaging lapsed clients to handling pricing conversations, this toolkit is a game-changer." – James T., Online Coach
    </blockquote>
  </section>

</main>

<footer class="bg-gray-800 text-white text-center py-6">
  &copy; 2025 Fitness Communication Mastery | Created for passionate fitness pros everywhere.
</footer>

<script>
  // Toggle Script Visibility
  function toggleScript(id) {
    const box = document.getElementById(id);
    const arrow = document.getElementById("arrow-" + id);
    const isVisible = box.classList.contains("hidden");
    if (isVisible) {
      box.classList.remove("hidden");
      arrow.style.transform = "rotate(180deg)";
    } else {
      box.classList.add("hidden");
      arrow.style.transform = "rotate(0deg)";
    }
  }

  // Submit Form to Google Sheets
  document.getElementById("emailForm").addEventListener("submit", function(e) {
    e.preventDefault();

    const email = document.getElementById("emailInput").value;

    fetch('https://script.google.com/macros/s/XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX/exec', {
      method: 'POST',
      mode: 'no-cors',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email: email })
    })
    .then(() => {
      alert("Thanks! Redirecting...");
      window.location.href = "thank-you.html"; // Optional: Create a thank-you page
    })
    .catch(err => {
      console.error("Error:", err);
      alert("Something went wrong. Please try again.");
    });
  });
</script>

</body>
</html>