package main

import "core:fmt"
import "core:strings"

// Colors (ANSI escape codes for terminal output)
BITCOIN_ORANGE :: "\x1b[38;2;247;147;26m"
BLACK :: "\x1b[38;2;18;18;18m"
GRAY :: "\x1b[38;2;128;128;128m"
WHITE :: "\x1b[38;2;255;255;255m"
RESET :: "\x1b[0m"
BOLD :: "\x1b[1m"

// Print a header with Bitcoin theme
print_header :: proc() {
    fmt.println(BITCOIN_ORANGE, BOLD, "╔══════════════════════════════════════════════════════════╗", RESET)
    fmt.println(BITCOIN_ORANGE, BOLD, "║                   BITCOIN DASHBOARD                       ║", RESET)
    fmt.println(BITCOIN_ORANGE, BOLD, "║                     PHOENIXBIT                           ║", RESET)
    fmt.println(BITCOIN_ORANGE, BOLD, "╚══════════════════════════════════════════════════════════╝", RESET)
}

// Print a section header
print_section :: proc(title: string) {
    fmt.println()
    fmt.println(BITCOIN_ORANGE, "▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓", RESET)
    fmt.println(WHITE, BOLD, title, RESET)
    fmt.println(BITCOIN_ORANGE, "▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓", RESET)
}

// Print a metric with label
print_metric :: proc(label: string, value: string) {
    fmt.printf("%s%s:%s %s%s%s\n", GRAY, label, RESET, BITCOIN_ORANGE, value, RESET)
}

// Print a table of companies
print_companies_table :: proc(companies: []Company) {
    // Print table header
    fmt.println()
    fmt.printf("%s%-20s %-6s %-15s %-10s %-12s%s\n", 
        BOLD, "COMPANY", "TICKER", "BTC HOLDINGS", "% TREASURY", "FIRST PURCHASE", RESET)
    fmt.println(GRAY, "────────────────────────────────────────────────────────────────────", RESET)
    
    // Print each company
    for company in companies {
        fmt.printf("%-20s %-6s %s%-15.0f%s %-10.1f%% %-12s\n", 
            company.name, 
            company.ticker, 
            BITCOIN_ORANGE, company.bitcoin_holdings, RESET,
            company.treasury_percentage,
            company.first_purchase_date)
    }
}

// Print a simple horizontal bar chart for industry breakdown
print_industry_chart :: proc(breakdown: map[string]f64) {
    fmt.println()
    fmt.println(BOLD, "INDUSTRY BREAKDOWN (% of total holdings)", RESET)
    fmt.println(GRAY, "────────────────────────────────────────────────────────────────────", RESET)
    
    for industry, percentage in breakdown {
        bar_length := int(percentage / 2)
        bar := strings.repeat("█", bar_length)
        fmt.printf("%-20s %s%s%s %.1f%%\n", industry, BITCOIN_ORANGE, bar, RESET, percentage)
    }
}

// Print a simple line chart for historical data
print_historical_chart :: proc(data: []Historical_Entry) {
    fmt.println()
    fmt.println(BOLD, "HISTORICAL BITCOIN HOLDINGS GROWTH (in thousands BTC)", RESET)
    fmt.println(GRAY, "────────────────────────────────────────────────────────────────────", RESET)
    
    max_amount: f64 = 0
    for entry in data {
        if entry.amount > max_amount {
            max_amount = entry.amount
        }
    }
    
    scale_factor := 50.0 / max_amount
    
    for entry in data {
        bar_length := int(entry.amount * scale_factor)
        bar := strings.repeat("■", bar_length)
        fmt.printf("%s: %s%s%s %.0f\n", entry.date, BITCOIN_ORANGE, bar, RESET, entry.amount / 1000)
    }
}

// Display the main dashboard
display_dashboard :: proc(data: Dashboard_Data) {
    print_header()
    
    // Print summary metrics
    print_section("SUMMARY METRICS")
    print_metric("Total Bitcoin on Corporate Balance Sheets", fmt.tprintf("%.0f BTC", data.total_bitcoin))
    print_metric("Number of Companies Holding Bitcoin", fmt.tprintf("%d", data.total_companies))
    print_metric("Percentage of Bitcoin Supply", fmt.tprintf("%.1f%%", data.bitcoin_supply_percentage))
    
    // Print companies table
    print_section("TOP COMPANIES BY BITCOIN HOLDINGS")
    print_companies_table(data.companies)
    
    // Print industry breakdown
    print_section("INDUSTRY BREAKDOWN")
    print_industry_chart(data.industry_breakdown)
    
    // Print historical chart
    print_section("HISTORICAL GROWTH")
    print_historical_chart(data.historical_total)
    
    fmt.println()
    fmt.println(GRAY, "Press Enter to exit...", RESET)
}
