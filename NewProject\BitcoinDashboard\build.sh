#!/bin/bash

echo "Building Bitcoin Dashboard App..."

# Check if <PERSON><PERSON> is installed
if ! command -v odin &> /dev/null; then
    echo "Odin compiler not found. Please install Odin first."
    echo "Visit https://odin-lang.org/docs/install/ for installation instructions."
    exit 1
fi

# Build the application
odin build . -out:bitcoin_dashboard

if [ $? -ne 0 ]; then
    echo "Build failed."
    exit 1
fi

echo "Build successful! Run ./bitcoin_dashboard to start the application."
chmod +x bitcoin_dashboard
