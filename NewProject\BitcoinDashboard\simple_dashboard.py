#!/usr/bin/env python3
"""
Bitcoin Dashboard - PhoenixBit (Simple Version)
A terminal-based application for tracking organizations with Bitcoin on their balance sheets.
"""

import json
import os
from datetime import datetime

# Bitcoin theme colors (ANSI escape codes for terminal)
BITCOIN_ORANGE = "\033[38;2;247;147;26m"
RESET = "\033[0m"
BOLD = "\033[1m"
UNDERLINE = "\033[4m"

def load_data():
    """Load sample data from JSON file."""
    try:
        with open('sample_data.json', 'r') as file:
            return json.load(file)
    except FileNotFoundError:
        print(f"{BITCOIN_ORANGE}Error: sample_data.json not found.{RESET}")
        print("Using default data instead.")
        
        # Default data if file not found
        return {
            "companies": [
                {
                    "name": "MicroStrategy",
                    "ticker": "MSTR",
                    "bitcoin_holdings": 140000,
                    "treasury_percentage": 95,
                    "first_purchase_date": "2020-08-11",
                    "industry": "Software"
                },
                {
                    "name": "Tesla",
                    "ticker": "TSLA",
                    "bitcoin_holdings": 42000,
                    "treasury_percentage": 15,
                    "first_purchase_date": "2021-02-08",
                    "industry": "Automotive"
                },
                {
                    "name": "Galaxy Digital",
                    "ticker": "GLXY",
                    "bitcoin_holdings": 16400,
                    "treasury_percentage": 70,
                    "first_purchase_date": "2018-11-05",
                    "industry": "Financial Technology"
                }
            ],
            "total_bitcoin": 1456789,
            "total_companies": 42,
            "bitcoin_supply_percentage": 6.9,
            "industry_breakdown": {
                "Software": 32,
                "Financial Technology": 28,
                "Mining": 15,
                "Automotive": 8,
                "Other": 17
            }
        }

def print_header():
    """Print the application header."""
    print("\n" + "=" * 80)
    print(f"{BITCOIN_ORANGE}{BOLD}BITCOIN DASHBOARD - PHOENIXBIT{RESET}")
    print(f"Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
    print("=" * 80 + "\n")

def print_summary(data):
    """Print summary metrics."""
    print(f"{BITCOIN_ORANGE}{BOLD}{UNDERLINE}SUMMARY METRICS{RESET}\n")
    print(f"Total Bitcoin on Balance Sheets: {BITCOIN_ORANGE}{data['total_bitcoin']:,} BTC{RESET}")
    print(f"Number of Companies: {BITCOIN_ORANGE}{data['total_companies']}{RESET}")
    print(f"Percentage of Bitcoin Supply: {BITCOIN_ORANGE}{data['bitcoin_supply_percentage']}%{RESET}\n")

def print_companies(data):
    """Print companies table."""
    print(f"{BITCOIN_ORANGE}{BOLD}{UNDERLINE}TOP COMPANIES BY BITCOIN HOLDINGS{RESET}\n")
    
    # Print table header
    print(f"{'Company':<20} {'Ticker':<8} {'BTC Holdings':<15} {'% Treasury':<12} {'First Purchase':<12} {'Industry':<20}")
    print("-" * 90)
    
    # Sort companies by holdings
    sorted_companies = sorted(data["companies"], key=lambda x: x["bitcoin_holdings"], reverse=True)
    
    # Print each company
    for company in sorted_companies:
        print(f"{company['name']:<20} {company['ticker']:<8} {BITCOIN_ORANGE}{company['bitcoin_holdings']:,} BTC{RESET:<15} {company['treasury_percentage']}%{:<12} {company['first_purchase_date']:<12} {company['industry']:<20}")
    
    print()

def print_industry_breakdown(data):
    """Print industry breakdown."""
    print(f"{BITCOIN_ORANGE}{BOLD}{UNDERLINE}INDUSTRY BREAKDOWN{RESET}\n")
    
    # Print each industry
    for industry, percentage in data["industry_breakdown"].items():
        bar_length = int(percentage / 2)
        bar = "█" * bar_length
        print(f"{industry:<20} {BITCOIN_ORANGE}{bar}{RESET} {percentage}%")
    
    print()

def main():
    """Main function."""
    # Clear screen
    os.system('cls' if os.name == 'nt' else 'clear')
    
    # Load data
    data = load_data()
    
    # Print dashboard
    print_header()
    print_summary(data)
    print_companies(data)
    print_industry_breakdown(data)
    
    # Wait for user input
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
