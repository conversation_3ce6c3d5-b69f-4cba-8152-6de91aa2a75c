@echo off
echo Building Bitcoin Dashboard App...

REM Check if Odin is installed
where odin >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Odin compiler not found. Please install Odin first.
    echo Visit https://odin-lang.org/docs/install/ for installation instructions.
    exit /b 1
)

REM Build the application
odin build . -out:bitcoin_dashboard.exe

if %ERRORLEVEL% neq 0 (
    echo Build failed.
    exit /b 1
)

echo Build successful! Run bitcoin_dashboard.exe to start the application.
