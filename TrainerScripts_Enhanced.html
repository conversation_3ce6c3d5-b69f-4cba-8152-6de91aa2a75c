<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrainerScripts - Professional Communication for Fitness Pros</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#F7931A',
                        dark: '#1A1A1A',
                        light: '#F3F4F6'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #1A1A1A 0%, #F7931A 100%);
        }
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .script-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .script-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="font-sans bg-gray-50">
    <!-- Hero Section -->
    <section class="gradient-bg min-h-screen flex items-center justify-center relative overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-20 left-20 w-72 h-72 bg-white rounded-full mix-blend-multiply filter blur-xl floating-animation"></div>
            <div class="absolute top-40 right-20 w-72 h-72 bg-primary rounded-full mix-blend-multiply filter blur-xl floating-animation" style="animation-delay: 2s;"></div>
            <div class="absolute -bottom-8 left-40 w-72 h-72 bg-white rounded-full mix-blend-multiply filter blur-xl floating-animation" style="animation-delay: 4s;"></div>
        </div>
        
        <div class="container mx-auto px-6 text-center relative z-10">
            <div class="fade-in">
                <h1 class="text-6xl md:text-7xl font-bold text-white mb-6 leading-tight">
                    TrainerScripts
                </h1>
                <p class="text-xl md:text-2xl text-gray-200 mb-8 max-w-3xl mx-auto leading-relaxed">
                    Transform difficult client conversations into breakthrough moments with 30 professionally crafted communication scripts
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
                    <button class="bg-primary hover:bg-orange-600 text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">
                        Get Instant Access
                    </button>
                    <button class="glass-effect text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 hover:bg-white hover:bg-opacity-20">
                        Watch Demo
                    </button>
                </div>
                <div class="flex items-center justify-center text-gray-300">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <span>Trusted by 500+ fitness professionals</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    Why Trainers Choose TrainerScripts
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Stop winging difficult conversations. Our proven scripts help you handle every client scenario with confidence and professionalism.
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="script-card bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
                    <div class="w-16 h-16 bg-primary bg-opacity-10 rounded-2xl flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Boost Client Retention</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Address motivation issues, missed sessions, and plateaus before they lead to client drop-off. Our scripts help you keep clients engaged long-term.
                    </p>
                </div>
                
                <div class="script-card bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
                    <div class="w-16 h-16 bg-primary bg-opacity-10 rounded-2xl flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Increase Revenue</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Professional templates for upselling packages, handling price objections, and introducing new services. Turn conversations into conversions.
                    </p>
                </div>
                
                <div class="script-card bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
                    <div class="w-16 h-16 bg-primary bg-opacity-10 rounded-2xl flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Save Time</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Stop reinventing the wheel with every client conversation. Our templates save you hours of preparation while ensuring professional communication.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Script Preview Section -->
    <section class="py-20 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    30 Professional Scripts
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Every scenario covered. Every conversation handled with confidence.
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="script-card bg-white p-6 rounded-xl shadow-lg border border-gray-100">
                    <div class="flex items-center mb-4">
                        <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">1</span>
                        <h3 class="font-bold text-gray-900">Breaking Through Fitness Burnout</h3>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">Re-engage clients who are losing motivation or showing signs of burnout.</p>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <p class="text-sm italic text-gray-700">"[Client Name], I've noticed you've been feeling less enthusiastic about your training lately..."</p>
                    </div>
                </div>
                
                <div class="script-card bg-white p-6 rounded-xl shadow-lg border border-gray-100">
                    <div class="flex items-center mb-4">
                        <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">2</span>
                        <h3 class="font-bold text-gray-900">Addressing Missed Sessions</h3>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">Tactfully address missed appointments without sounding accusatory.</p>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <p class="text-sm italic text-gray-700">"I noticed we missed our session scheduled for [date/time]..."</p>
                    </div>
                </div>
                
                <div class="script-card bg-white p-6 rounded-xl shadow-lg border border-gray-100">
                    <div class="flex items-center mb-4">
                        <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">3</span>
                        <h3 class="font-bold text-gray-900">Handling Price Objections</h3>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">Professional responses to concerns about training costs and value.</p>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <p class="text-sm italic text-gray-700">"I understand budget is an important consideration..."</p>
                    </div>
                </div>
                
                <div class="script-card bg-white p-6 rounded-xl shadow-lg border border-gray-100">
                    <div class="flex items-center mb-4">
                        <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">4</span>
                        <h3 class="font-bold text-gray-900">Nutrition Conversations</h3>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">Stay within scope of practice while providing valuable guidance.</p>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <p class="text-sm italic text-gray-700">"While I'm not a dietitian, I can share some general principles..."</p>
                    </div>
                </div>
                
                <div class="script-card bg-white p-6 rounded-xl shadow-lg border border-gray-100">
                    <div class="flex items-center mb-4">
                        <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">5</span>
                        <h3 class="font-bold text-gray-900">Progress Without Weight Focus</h3>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">Redirect clients fixated on the scale to meaningful metrics.</p>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <p class="text-sm italic text-gray-700">"While the scale can be one data point, I love looking at..."</p>
                    </div>
                </div>
                
                <div class="script-card bg-white p-6 rounded-xl shadow-lg border border-gray-100">
                    <div class="flex items-center mb-4">
                        <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-semibold mr-3">+25</span>
                        <h3 class="font-bold text-gray-900">And 25 More Scripts</h3>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">Complete coverage of every client scenario you'll encounter.</p>
                    <div class="bg-primary bg-opacity-10 p-4 rounded-lg">
                        <p class="text-sm font-semibold text-primary">Get instant access to all 30 scripts</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    What Trainers Are Saying
                </h2>
                <p class="text-xl text-gray-600">Real results from real fitness professionals</p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-gray-50 p-8 rounded-2xl">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Sarah M." class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-bold text-gray-900">Sarah M.</h4>
                            <p class="text-gray-600 text-sm">Personal Trainer, 8 years</p>
                        </div>
                    </div>
                    <div class="flex mb-4">
                        <span class="text-yellow-400">★★★★★</span>
                    </div>
                    <p class="text-gray-700 italic">"The burnout script alone helped me save three long-term clients who were about to quit. These templates have completely transformed how I communicate with my clients."</p>
                </div>

                <div class="bg-gray-50 p-8 rounded-2xl">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="James T." class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-bold text-gray-900">James T.</h4>
                            <p class="text-gray-600 text-sm">Online Coach</p>
                        </div>
                    </div>
                    <div class="flex mb-4">
                        <span class="text-yellow-400">★★★★★</span>
                    </div>
                    <p class="text-gray-700 italic">"From re-engaging lapsed clients to handling pricing conversations, this toolkit is a game-changer. I used to dread certain client interactions."</p>
                </div>

                <div class="bg-gray-50 p-8 rounded-2xl">
                    <div class="flex items-center mb-6">
                        <img src="https://randomuser.me/api/portraits/men/67.jpg" alt="Michael R." class="w-12 h-12 rounded-full mr-4">
                        <div>
                            <h4 class="font-bold text-gray-900">Michael R.</h4>
                            <p class="text-gray-600 text-sm">Studio Owner</p>
                        </div>
                    </div>
                    <div class="flex mb-4">
                        <span class="text-yellow-400">★★★★★</span>
                    </div>
                    <p class="text-gray-700 italic">"Client retention is up 22% since implementing these scripts. All my trainers feel more confident handling difficult conversations."</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="py-20 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    Simple, Transparent Pricing
                </h2>
                <p class="text-xl text-gray-600">Everything you need to master client communication</p>
            </div>

            <div class="max-w-lg mx-auto">
                <div class="bg-white rounded-3xl shadow-2xl overflow-hidden border-4 border-primary">
                    <div class="bg-primary text-white text-center py-4">
                        <span class="font-bold text-lg">LAUNCH SPECIAL - 60% OFF</span>
                    </div>
                    <div class="p-8 text-center">
                        <h3 class="text-3xl font-bold text-gray-900 mb-4">TrainerScripts Complete</h3>
                        <div class="mb-6">
                            <span class="text-5xl font-bold text-gray-900">$47</span>
                            <span class="text-xl text-gray-500 line-through ml-2">$97</span>
                        </div>
                        <ul class="text-left space-y-4 mb-8">
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-primary mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                All 30 professional scripts
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-primary mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Print-ready PDF formats
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-primary mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Implementation guide
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-primary mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Lifetime updates
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-primary mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                30-day money-back guarantee
                            </li>
                        </ul>
                        <button class="w-full bg-primary hover:bg-orange-600 text-white py-4 rounded-full text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">
                            Get Instant Access Now
                        </button>
                        <p class="text-gray-500 text-sm mt-4">One-time payment • Instant download</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    Frequently Asked Questions
                </h2>
            </div>

            <div class="max-w-3xl mx-auto space-y-6">
                <div class="bg-gray-50 rounded-xl p-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-3">How customizable are the scripts?</h3>
                    <p class="text-gray-700">All scripts are fully customizable templates. You can edit any text, add your own sections, and adapt them to your specific training style and client needs.</p>
                </div>

                <div class="bg-gray-50 rounded-xl p-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Can I use these for both email and in-person conversations?</h3>
                    <p class="text-gray-700">Absolutely! The scripts are designed to work for written communication (email, messaging) and as conversation guides for in-person or video consultations.</p>
                </div>

                <div class="bg-gray-50 rounded-xl p-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Are the scripts evidence-based?</h3>
                    <p class="text-gray-700">Yes. All scripts are created by experienced fitness professionals and based on proven client communication strategies and behavioral psychology principles.</p>
                </div>

                <div class="bg-gray-50 rounded-xl p-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-3">What if I'm not satisfied?</h3>
                    <p class="text-gray-700">We offer a 30-day money-back guarantee. If you're not completely satisfied with the scripts, we'll refund your purchase, no questions asked.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA Section -->
    <section class="py-20 gradient-bg">
        <div class="container mx-auto px-6 text-center">
            <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
                Ready to Transform Your Client Conversations?
            </h2>
            <p class="text-xl text-gray-200 mb-8 max-w-2xl mx-auto">
                Join 500+ fitness professionals who've already upgraded their communication skills
            </p>
            <button class="bg-white text-dark px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl mb-4">
                Get All 30 Scripts Now - $47
            </button>
            <p class="text-gray-300">30-day money-back guarantee • Instant download</p>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-12">
        <div class="container mx-auto px-6">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <h3 class="text-2xl font-bold text-primary mb-2">TrainerScripts</h3>
                    <p class="text-gray-400">Professional communication for fitness pros</p>
                </div>
                <div class="flex space-x-6">
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">Terms</a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">Privacy</a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">Contact</a>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-400">&copy; 2023 TrainerScripts by PhoenixBit. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Add scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        }, observerOptions);

        // Observe all animated elements
        document.querySelectorAll('.script-card, .feature-card').forEach(card => {
            observer.observe(card);
        });

        // CTA Button interactions
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('click', function(e) {
                if (this.textContent.includes('Get All 30 Scripts') || this.textContent.includes('Get Instant Access')) {
                    e.preventDefault();

                    // Create a simple modal or redirect to payment
                    const modal = document.createElement('div');
                    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                    modal.innerHTML = `
                        <div class="bg-white p-8 rounded-2xl max-w-md mx-4">
                            <h3 class="text-2xl font-bold mb-4">Get TrainerScripts</h3>
                            <p class="text-gray-600 mb-6">Enter your email to receive instant access to all 30 professional scripts.</p>
                            <form id="purchase-form" class="space-y-4">
                                <input type="email" placeholder="Your email address" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                <button type="submit"
                                        class="w-full bg-primary hover:bg-orange-600 text-white py-3 rounded-lg font-semibold transition-colors">
                                    Complete Purchase - $47
                                </button>
                            </form>
                            <button onclick="this.parentElement.parentElement.remove()"
                                    class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    `;

                    document.body.appendChild(modal);

                    // Handle form submission
                    document.getElementById('purchase-form').addEventListener('submit', function(e) {
                        e.preventDefault();
                        const email = this.querySelector('input[type="email"]').value;

                        // In a real implementation, this would integrate with a payment processor
                        alert(`Thanks for your purchase! Check your email (${email}) for download instructions.`);
                        modal.remove();
                    });
                }
            });
        });

        // Add a subtle parallax effect to the hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.gradient-bg');
            if (parallax) {
                const speed = scrolled * 0.5;
                parallax.style.transform = `translateY(${speed}px)`;
            }
        });

        // Add typing effect to hero headline
        function typeWriter(element, text, speed = 100) {
            let i = 0;
            element.innerHTML = '';

            function type() {
                if (i < text.length) {
                    element.innerHTML += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                }
            }
            type();
        }

        // Initialize typing effect when page loads
        window.addEventListener('load', () => {
            const headline = document.querySelector('h1');
            if (headline) {
                const originalText = headline.textContent;
                typeWriter(headline, originalText, 150);
            }
        });

        // Add counter animation for testimonials
        function animateCounter(element, target, duration = 2000) {
            let start = 0;
            const increment = target / (duration / 16);

            function updateCounter() {
                start += increment;
                if (start < target) {
                    element.textContent = Math.floor(start);
                    requestAnimationFrame(updateCounter);
                } else {
                    element.textContent = target;
                }
            }
            updateCounter();
        }

        // Trigger counter when testimonials section is visible
        const testimonialObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const counters = entry.target.querySelectorAll('[data-count]');
                    counters.forEach(counter => {
                        const target = parseInt(counter.getAttribute('data-count'));
                        animateCounter(counter, target);
                    });
                    testimonialObserver.unobserve(entry.target);
                }
            });
        });

        const testimonialsSection = document.querySelector('.testimonials-section');
        if (testimonialsSection) {
            testimonialObserver.observe(testimonialsSection);
        }
    </script>
</body>
</html>
