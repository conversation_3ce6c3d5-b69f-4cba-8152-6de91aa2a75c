/**
 * Simple HTTP server for the Bitcoin Dashboard using Node.js
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// Configuration
const PORT = 8000;

// Create server
const server = http.createServer((req, res) => {
  console.log(`[SERVER] Request: ${req.url}`);
  
  // Set default URL to dashboard.html
  let url = req.url;
  if (url === '/') {
    url = '/dashboard.html';
  }
  
  // Get file path
  const filePath = path.join(__dirname, url);
  
  // Get file extension
  const extname = path.extname(filePath);
  
  // Set content type based on file extension
  let contentType = 'text/html';
  switch (extname) {
    case '.js':
      contentType = 'text/javascript';
      break;
    case '.css':
      contentType = 'text/css';
      break;
    case '.json':
      contentType = 'application/json';
      break;
    case '.png':
      contentType = 'image/png';
      break;
    case '.jpg':
      contentType = 'image/jpg';
      break;
  }
  
  // Read file
  fs.readFile(filePath, (err, content) => {
    if (err) {
      if (err.code === 'ENOENT') {
        // Page not found
        fs.readFile(path.join(__dirname, '404.html'), (err, content) => {
          res.writeHead(404, { 'Content-Type': 'text/html' });
          res.end(content, 'utf8');
        });
      } else {
        // Server error
        res.writeHead(500);
        res.end(`Server Error: ${err.code}`);
      }
    } else {
      // Success
      res.writeHead(200, { 'Content-Type': contentType });
      res.end(content, 'utf8');
    }
  });
});

// Start server
server.listen(PORT, () => {
  console.log(`[SERVER] Server running at http://localhost:${PORT}/`);
  console.log(`[SERVER] Opening dashboard in browser...`);
  
  // Open browser
  const url = `http://localhost:${PORT}/dashboard.html`;
  const start = (process.platform === 'darwin' ? 'open' : process.platform === 'win32' ? 'start' : 'xdg-open');
  exec(`${start} ${url}`);
  
  console.log(`[SERVER] Press Ctrl+C to stop the server`);
});
