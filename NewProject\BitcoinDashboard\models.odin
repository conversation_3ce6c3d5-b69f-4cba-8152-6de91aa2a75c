package main

// Company represents an organization with Bitcoin on its balance sheet
Company :: struct {
    name: string,
    ticker: string,
    bitcoin_holdings: f64,
    treasury_percentage: f64,
    first_purchase_date: string,
    industry: string,
    market_cap: f64,
}

// Purchase represents a Bitcoin acquisition event
Purchase :: struct {
    date: string,
    amount: f64,
    price_per_btc: f64,
}

// Historical_Entry represents a point in time with total Bitcoin holdings
Historical_Entry :: struct {
    date: string,
    amount: f64,
}

// Dashboard_Data contains all the data needed for the dashboard
Dashboard_Data :: struct {
    companies: []Company,
    total_bitcoin: f64,
    total_companies: int,
    bitcoin_supply_percentage: f64,
    industry_breakdown: map[string]f64,
    historical_total: []Historical_Entry,
}

// Create sample dashboard data
create_sample_data :: proc() -> Dashboard_Data {
    data: Dashboard_Data
    
    // Create sample companies
    companies := make([]Company, 5)
    
    companies[0] = Company{
        name = "MicroStrategy",
        ticker = "MSTR",
        bitcoin_holdings = 140000,
        treasury_percentage = 95,
        first_purchase_date = "2020-08-11",
        industry = "Software",
        market_cap = 15000000000,
    }
    
    companies[1] = Company{
        name = "Tesla",
        ticker = "TSLA",
        bitcoin_holdings = 42000,
        treasury_percentage = 15,
        first_purchase_date = "2021-02-08",
        industry = "Automotive",
        market_cap = 650000000000,
    }
    
    companies[2] = Company{
        name = "Galaxy Digital",
        ticker = "GLXY",
        bitcoin_holdings = 16400,
        treasury_percentage = 70,
        first_purchase_date = "2018-11-05",
        industry = "Financial Technology",
        market_cap = 2800000000,
    }
    
    companies[3] = Company{
        name = "Marathon Digital",
        ticker = "MARA",
        bitcoin_holdings = 13380,
        treasury_percentage = 80,
        first_purchase_date = "2021-01-15",
        industry = "Mining",
        market_cap = 3500000000,
    }
    
    companies[4] = Company{
        name = "Coinbase",
        ticker = "COIN",
        bitcoin_holdings = 9000,
        treasury_percentage = 40,
        first_purchase_date = "2019-05-20",
        industry = "Financial Technology",
        market_cap = 12000000000,
    }
    
    data.companies = companies
    data.total_bitcoin = 1456789
    data.total_companies = 42
    data.bitcoin_supply_percentage = 6.9
    
    // Create industry breakdown
    industry_breakdown := make(map[string]f64)
    industry_breakdown["Software"] = 32
    industry_breakdown["Financial Technology"] = 28
    industry_breakdown["Mining"] = 15
    industry_breakdown["Automotive"] = 8
    industry_breakdown["Other"] = 17
    
    data.industry_breakdown = industry_breakdown
    
    // Create historical data
    historical := make([]Historical_Entry, 8)
    
    historical[0] = Historical_Entry{"2020-08", 85000}
    historical[1] = Historical_Entry{"2020-12", 225000}
    historical[2] = Historical_Entry{"2021-03", 815000}
    historical[3] = Historical_Entry{"2021-06", 1120000}
    historical[4] = Historical_Entry{"2021-12", 1320000}
    historical[5] = Historical_Entry{"2022-06", 1380000}
    historical[6] = Historical_Entry{"2022-12", 1410000}
    historical[7] = Historical_Entry{"2023-06", 1456789}
    
    data.historical_total = historical
    
    return data
}
