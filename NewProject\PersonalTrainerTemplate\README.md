# Personal Trainer Template

A responsive website template for personal trainers built with Tailwind CSS.

## Fixing the `@tailwind` Directive Errors

If you're seeing errors with the `@tailwind` directives, follow these steps to fix them:

### 1. Make Sure Tailwind CSS is Installed

```bash
npm install
```

This will install all the dependencies defined in package.json, including Tailwind CSS.

### 2. Build the CSS

```bash
npm run build
```

This will process your Tailwind directives and generate the output.css file.

### 3. Watch for Changes During Development

```bash
npm run watch
```

This will watch for changes in your CSS and HTML files and rebuild the CSS automatically.

## Common Issues and Solutions

### Error: `@tailwind base;` not recognized

This error typically occurs when:

1. **Tailwind CSS is not installed**: Run `npm install` to install all dependencies.
2. **PostCSS is not configured correctly**: Make sure you have a `postcss.config.js` file.
3. **Build process is not set up**: Run `npm run build` to process the CSS.

### Indentation Issues

Make sure your CSS file has the Tailwind directives at the beginning of the file without indentation:

```css
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Your custom styles below */
```

### File Structure

Make sure your file structure is correct:

- `styles.css` - Contains the Tailwind directives
- `output.css` - Generated CSS file (referenced in your HTML)
- `tailwind.config.js` - Tailwind configuration
- `postcss.config.js` - PostCSS configuration
- `index.html` - HTML file that references the output.css

## Additional Resources

- [Tailwind CSS Installation Guide](https://tailwindcss.com/docs/installation)
- [PostCSS Documentation](https://postcss.org/)
- [Troubleshooting Tailwind CSS](https://tailwindcss.com/docs/troubleshooting)
