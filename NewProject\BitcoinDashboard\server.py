#!/usr/bin/env python3
"""
Simple HTTP server for the Bitcoin Dashboard
"""

import http.server
import socketserver
import os
import webbrowser
from urllib.parse import urlparse

# Configuration
PORT = 8000
DIRECTORY = os.path.dirname(os.path.abspath(__file__))

class Handler(http.server.SimpleHTTPRequestHandler):
    """Custom request handler for our server."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=DIRECTORY, **kwargs)
    
    def log_message(self, format, *args):
        """Log an arbitrary message."""
        print(f"[SERVER] {self.address_string()} - {format % args}")
    
    def end_headers(self):
        """Send standard headers and add CORS headers."""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def run_server():
    """Run the HTTP server."""
    handler = Handler
    
    with socketserver.TCPServer(("", PORT), handler) as httpd:
        print(f"[SERVER] Serving at http://localhost:{PORT}")
        print(f"[SERVER] Serving from directory: {DIRECTORY}")
        print(f"[SERVER] Opening dashboard in browser...")
        
        # Open the dashboard in the default browser
        webbrowser.open(f"http://localhost:{PORT}/dashboard.html")
        
        print(f"[SERVER] Press Ctrl+C to stop the server")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print(f"[SERVER] Server stopped.")

if __name__ == "__main__":
    run_server()
