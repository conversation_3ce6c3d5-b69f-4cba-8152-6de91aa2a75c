@echo off
echo Bitcoin Dashboard - Diagnostic Tool
echo ================================
echo.

echo Checking Python installation...
where python
if %ERRORLEVEL% neq 0 (
    echo Python not found in PATH. Please install Python or add it to your PATH.
    goto :end
) else (
    echo Python found. Checking version:
    python --version
)

echo.
echo Checking if we can run a simple Python script...
echo print("Hello from Python!") > test_simple.py
python test_simple.py
if %ERRORLEVEL% neq 0 (
    echo Failed to run simple Python script.
) else (
    echo Successfully ran simple Python script.
)

echo.
echo Checking if sample_data.json exists...
if exist sample_data.json (
    echo sample_data.json found.
) else (
    echo sample_data.json NOT found.
)

echo.
echo Checking if main.py exists...
if exist main.py (
    echo main.py found.
) else (
    echo main.py NOT found.
)

echo.
echo Checking if simple_dashboard.py exists...
if exist simple_dashboard.py (
    echo simple_dashboard.py found.
) else (
    echo simple_dashboard.py NOT found.
)

echo.
echo Checking Python modules...
python -c "import tkinter; print('tkinter is available')" 2>nul
if %ERRORLEVEL% neq 0 echo tkinter is NOT available

python -c "import matplotlib; print('matplotlib is available')" 2>nul
if %ERRORLEVEL% neq 0 echo matplotlib is NOT available

python -c "import json; print('json is available')" 2>nul
if %ERRORLEVEL% neq 0 echo json is NOT available

:end
echo.
echo Diagnostic complete.
echo Press any key to exit...
pause > nul
