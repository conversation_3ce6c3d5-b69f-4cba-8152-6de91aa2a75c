<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>TrainerScripts - Communication Mastery for Fitness Pros</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 0;
      background-color: #f9f9f9;
      color: #333;
    }

    header {
      background: linear-gradient(to right, #1A1A1A, #F7931A);
      color: white;
      text-align: center;
      padding: 3rem 1rem;
    }

    header h1 {
      font-size: 2.5rem;
      margin-bottom: 0.5rem;
    }

    header p {
      font-size: 1.2rem;
      max-width: 700px;
      margin: 0 auto;
    }

    .container {
      max-width: 1000px;
      margin: auto;
      padding: 2rem 1rem;
    }

    section {
      margin-bottom: 3rem;
    }

    h2 {
      color: #F7931A;
      border-bottom: 2px solid #F7931A;
      padding-bottom: 0.5rem;
    }

    ul {
      list-style-type: none;
      padding-left: 0;
    }

    ul li::before {
      content: "✅ ";
      color: #F7931A;
    }

    .script-box {
      background: white;
      border-left: 5px solid #F7931A;
      padding: 1rem;
      margin-bottom: 1rem;
      box-shadow: 0 2px 5px rgba(0,0,0,0.05);
      display: none;
      border-radius: 0 5px 5px 0;
    }

    .script-title {
      cursor: pointer;
      padding: 1rem;
      background-color: #f3f4f6;
      border-radius: 5px;
      font-weight: bold;
      transition: background 0.3s;
      margin-top: 0.5rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .script-title:hover {
      background-color: #e5e7eb;
    }
    
    .script-title::after {
      content: "+";
      font-size: 1.5rem;
      font-weight: bold;
    }
    
    .script-title.active::after {
      content: "-";
    }

    .download-section {
      background: #1A1A1A;
      color: white;
      padding: 2rem;
      text-align: center;
      border-radius: 8px;
      position: relative;
      overflow: hidden;
    }
    
    .download-section::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url('https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-1.2.1&auto=format&fit=crop&w=1500&q=80');
      background-size: cover;
      opacity: 0.1;
      z-index: 0;
    }

    .download-section > * {
      position: relative;
      z-index: 1;
    }

    .download-section h3 {
      margin-bottom: 1rem;
      font-size: 1.8rem;
      color: #F7931A;
    }

    .email-form input[type="email"] {
      padding: 0.7rem;
      width: 80%;
      max-width: 300px;
      border: 1px solid #ccc;
      border-radius: 4px 0 0 4px;
      margin-right: 0;
      border-right: none;
    }

    .email-form button {
      padding: 0.7rem 1.5rem;
      background-color: #F7931A;
      color: white;
      border: none;
      border-radius: 0 4px 4px 0;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    
    .email-form button:hover {
      background-color: #e67e00;
    }

    .testimonial {
      font-style: italic;
      margin-top: 1rem;
      color: #555;
      background: white;
      padding: 1.5rem;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      position: relative;
    }
    
    .testimonial::before {
      content: """;
      font-size: 4rem;
      position: absolute;
      top: -10px;
      left: 10px;
      color: #F7931A;
      opacity: 0.2;
    }
    
    .testimonial-author {
      font-weight: bold;
      color: #1A1A1A;
      margin-top: 0.5rem;
      text-align: right;
    }

    footer {
      text-align: center;
      padding: 1rem;
      background: #1A1A1A;
      color: white;
    }
    
    .script-categories {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin-bottom: 1rem;
    }
    
    .category-btn {
      padding: 0.5rem 1rem;
      background: #f3f4f6;
      border: none;
      border-radius: 20px;
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .category-btn:hover, .category-btn.active {
      background: #F7931A;
      color: white;
    }
    
    .script-count {
      background: #F7931A;
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-weight: bold;
      display: inline-block;
      margin-bottom: 1rem;
    }
    
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 1.5rem;
      margin-top: 2rem;
    }
    
    .feature-card {
      background: white;
      padding: 1.5rem;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      transition: transform 0.3s;
    }
    
    .feature-card:hover {
      transform: translateY(-5px);
    }
    
    .feature-icon {
      font-size: 2rem;
      color: #F7931A;
      margin-bottom: 1rem;
    }

    @media (max-width: 600px) {
      .email-form {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      
      .email-form input[type="email"],
      .email-form button {
        width: 100%;
        max-width: 300px;
        margin-bottom: 0.5rem;
        border-radius: 4px;
      }
      
      .features-grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>

  <header>
    <h1>TrainerScripts: Communication Mastery</h1>
    <p>Transform your client relationships with 30 proven communication scripts designed specifically for fitness professionals.</p>
  </header>

  <div class="container">

    <section>
      <h2>What's Inside the Toolkit?</h2>
      <div class="script-count">30 Professional Scripts</div>
      <ul>
        <li><strong>Comprehensive Coverage</strong>: Scripts for every stage of the client journey</li>
        <li><strong>Customizable Frameworks</strong>: Use them in emails, calls, or in-person sessions</li>
        <li><strong>Client-Centered Language</strong>: Build trust and clarity with proven communication</li>
        <li><strong>Action-Oriented Solutions</strong>: Keep clients moving forward with clear next steps</li>
        <li><strong>Print-Ready PDFs</strong>: Download and use immediately in your business</li>
      </ul>
      
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">🔄</div>
          <h3>Client Retention</h3>
          <p>Scripts specifically designed to address motivation issues, missed sessions, and plateaus that lead to client drop-off.</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">💰</div>
          <h3>Revenue Growth</h3>
          <p>Professional templates for upselling packages, handling price objections, and introducing new services.</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">⏱️</div>
          <h3>Time Saving</h3>
          <p>Stop reinventing the wheel with every client conversation. Our templates save you hours of preparation.</p>
        </div>
      </div>
    </section>

    <section>
      <h2>Preview Our Most Popular Scripts</h2>
      
      <div class="script-categories">
        <button class="category-btn active" data-category="all">All Scripts</button>
        <button class="category-btn" data-category="retention">Client Retention</button>
        <button class="category-btn" data-category="sales">Sales & Pricing</button>
        <button class="category-btn" data-category="nutrition">Nutrition</button>
        <button class="category-btn" data-category="motivation">Motivation</button>
      </div>

      <!-- Script 1 -->
      <div class="script-title" data-category="motivation" onclick="toggleScript('script1')">1. Breaking Through Fitness Burnout</div>
      <div id="script1" class="script-box">
        <strong>When to use:</strong> When a long-term client is losing motivation or showing signs of burnout.<br><br>
        <strong>Script:</strong><br>
        "[Client Name], I've noticed you've been feeling less enthusiastic about your training lately. That's completely normal after putting in so much effort over the past [time period]. Let's take a moment to acknowledge how far you've come before we talk about moving forward. Would you be open to exploring some ways we could reignite your enthusiasm while still working toward your goals?"
        <br><br>
        <strong>Key Elements:</strong>
        <ul>
          <li>Acknowledges effort and progress</li>
          <li>Normalizes burnout as part of the journey</li>
          <li>Offers collaborative problem-solving</li>
          <li>Maintains focus on long-term goals</li>
        </ul>
      </div>

      <!-- Script 2 -->
      <div class="script-title" data-category="retention" onclick="toggleScript('script2')">2. Setting Realistic Expectations During Onboarding</div>
      <div id="script2" class="script-box">
        <strong>When to use:</strong> First session with a new client.<br><br>
        <strong>Script:</strong><br>
        "Before we dive into your program, let's make sure we're aligned on what success looks like and how we'll get there. Based on your goals of [insert client goals], here's what I typically see with clients: [brief overview of realistic timeline/outcomes]. This means we'll need consistency with [specific requirements] and some patience as we work through the natural ups and downs of any fitness journey. Does that align with what you're expecting?"
        <br><br>
        <strong>Key Elements:</strong>
        <ul>
          <li>Establishes professional boundaries</li>
          <li>Sets clear expectations around results</li>
          <li>Outlines mutual responsibilities</li>
          <li>Creates accountability framework</li>
        </ul>
      </div>

      <!-- Script 3 -->
      <div class="script-title" data-category="retention" onclick="toggleScript('script3')">3. Addressing Missed Sessions</div>
      <div id="script3" class="script-box">
        <strong>When to use:</strong> Tactfully address missed appointments without sounding accusatory.<br><br>
        <strong>Script:</strong><br>
        "I noticed we missed our session scheduled for [date/time]. I completely understand that life gets busy sometimes. The important thing is maintaining your momentum. Would you prefer we reschedule this session, or would you like me to send you a simple check-in routine to keep things moving until our next appointment?"
        <br><br>
        <strong>Key Elements:</strong>
        <ul>
          <li>Expresses understanding without enabling</li>
          <li>Presents constructive options</li>
          <li>Focuses on maintaining progress</li>
          <li>Opens dialogue for pattern recognition</li>
        </ul>
      </div>

      <!-- Script 4 -->
      <div class="script-title" data-category="motivation" onclick="toggleScript('script4')">4. Discussing Progress Without Focusing on Weight</div>
      <div id="script4" class="script-box">
        <strong>When to use:</strong> For clients fixated on the scale or dealing with weight loss plateaus.<br><br>
        <strong>Script:</strong><br>
        "While the scale can be one data point, I actually love looking at all the other great progress markers we have to celebrate. For example, you've increased your squat by [amount], improved your sleep quality from [X] to [Y] hours per night, and mentioned feeling more confident in [specific situation]. These are all incredible indicators of positive body transformation beyond just numbers."
        <br><br>
        <strong>Key Elements:</strong>
        <ul>
          <li>Redirects focus to meaningful metrics</li>
          <li>Celebrates non-scale victories</li>
          <li>Validates progress holistically</li>
          <li>Educates on body composition changes</li>
        </ul>
      </div>

      <!-- Script 5 -->
      <div class="script-title" data-category="motivation" onclick="toggleScript('script5')">5. Introducing New Training Methods</div>
      <div id="script5" class="script-box">
        <strong>When to use:</strong> Helps trainers explain why it's time to change up workouts.<br><br>
        <strong>Script:</strong><br>
        "You've made such great progress with our current program that your body has adapted beautifully to the challenges we've provided. To continue making gains, we should introduce a new phase focused on [new goal/method]. Think of it like leveling up in a video game - you've completed stage 1 successfully, and now we unlock even better capabilities for stage 2!"
        <br><br>
        <strong>Key Elements:</strong>
        <ul>
          <li>Frames change as positive progression</li>
          <li>Explains physiological adaptation</li>
          <li>Creates excitement for next phase</li>
          <li>Maintains continuity in relationship</li>
        </ul>
      </div>

      <!-- Script 6 -->
      <div class="script-title" data-category="nutrition" onclick="toggleScript('script6')">6. Starting a Nutrition Conversation</div>
      <div id="script6" class="script-box">
        <strong>When to use:</strong> Trainers often avoid nutrition advice due to legal/ethical concerns.<br><br>
        <strong>Script:</strong><br>
        "I notice you're working hard with your training, and I wonder if you might benefit from some basic nutritional guidance to complement your efforts. While I'm not a dietitian, I can share some general principles that apply to most people trying to [client goal]. If you'd like more detailed plans though, I'd be happy to connect you with a trusted local/national professional who works well with active individuals."
        <br><br>
        <strong>Key Elements:</strong>
        <ul>
          <li>Respects scope of practice</li>
          <li>Offers value within limits</li>
          <li>Makes appropriate referral</li>
          <li>Connects physical systems</li>
        </ul>
      </div>

      <!-- Script 7 -->
      <div class="script-title" data-category="sales" onclick="toggleScript('script7')">7. Handling Price Increases</div>
      <div id="script7" class="script-box">
        <strong>When to use:</strong> A professional way to communicate financial updates.<br><br>
        <strong>Script:</strong><br>
        "As our training relationship has grown, we've been able to refine your program with [specific improvements/services added]. Effective [date], my rates will reflect these enhanced offerings across all clients. However, I'd love to offer you a special continuity option that includes [benefits] at a minimal increase, recognizing your loyalty and commitment to our process."
        <br><br>
        <strong>Key Elements:</strong>
        <ul>
          <li>Links price to value delivered</li>
          <li>Shows appreciation for loyalty</li>
          <li>Offers transitional incentive</li>
          <li>Maintains professional positioning</li>
        </ul>
      </div>

      <!-- Script 8 -->
      <div class="script-title" data-category="sales" onclick="toggleScript('script8')">8. Offering Group Program Upsells</div>
      <div id="script8" class="script-box">
        <strong>When to use:</strong> Introduces group training opportunities.<br><br>
        <strong>Script:</strong><br>
        "I've really enjoyed working with you individually, and I think you'd benefit greatly from connecting with others who share your goals. Our [program name] offers [key benefits] in a supportive community setting at a more accessible price point. You could continue your personal sessions while adding this as a supplement, or transition fully depending on your current priorities."
        <br><br>
        <strong>Key Elements:</strong>
        <ul>
          <li>Acknowledges existing relationship</li>
          <li>Positions as complementary option</li>
          <li>Provides flexibility choices</li>
          <li>Focuses on community benefits</li>
        </ul>
      </div>

      <p class="text-center mt-4">
        <a href="#download" class="btn btn-primary">View All 30 Scripts</a>
      </p>
    </section>

    <section class="download-section" id="download">
      <h3>Get Instant Access to All 30 Scripts</h3>
      <p>Enter your email below to receive the complete TrainerScripts toolkit + implementation guide.</p>
      <form class="email-form" id="access-form">
        <input type="email" placeholder="Your Email" required />
        <button type="submit">Get Instant Access</button>
      </form>
      <small>We respect your privacy. Unsubscribe at any time.</small>
    </section>

    <section>
      <h2>What Trainers Are Saying</h2>
      <div class="testimonial">
        "These scripts have completely changed how I interact with my clients. I feel more confident and prepared for any conversation. The burnout script alone helped me save three long-term clients who were about to quit."
        <div class="testimonial-author">– Sarah M., Personal Trainer</div>
      </div>
      <div class="testimonial">
        "From re-engaging lapsed clients to handling pricing conversations, this toolkit is a game-changer. I used to dread certain client interactions, but now I have a framework that makes these discussions productive and positive."
        <div class="testimonial-author">– James T., Online Coach</div>
      </div>
      <div class="testimonial">
        "I've required all trainers at my studio to use these scripts. Client retention is up 22% since implementing them, and our trainers feel more confident handling difficult conversations."
        <div class="testimonial-author">– Michael R., Fitness Studio Owner</div>
      </div>
    </section>

  </div>

  <footer>
    &copy; 2023 TrainerScripts | Created by PhoenixBit for passionate fitness pros everywhere.
  </footer>

  <script>
    // Toggle script visibility
    function toggleScript(id) {
      const scriptBox = document.getElementById(id);
      const scriptTitle = document.querySelector(`[onclick="toggleScript('${id}')"]`);
      
      // Close all other scripts
      document.querySelectorAll('.script-box').forEach(box => {
        if (box.id !== id) {
          box.style.display = 'none';
        }
      });
      
      document.querySelectorAll('.script-title').forEach(title => {
        if (title !== scriptTitle) {
          title.classList.remove('active');
        }
      });
      
      // Toggle current script
      if (scriptBox.style.display === 'block') {
        scriptBox.style.display = 'none';
        scriptTitle.classList.remove('active');
      } else {
        scriptBox.style.display = 'block';
        scriptTitle.classList.add('active');
      }
    }
    
    // Category filtering
    document.querySelectorAll('.category-btn').forEach(btn => {
      btn.addEventListener('click', function() {
        const category = this.getAttribute('data-category');
        
        // Update active button
        document.querySelectorAll('.category-btn').forEach(b => {
          b.classList.remove('active');
        });
        this.classList.add('active');
        
        // Filter scripts
        document.querySelectorAll('.script-title').forEach(script => {
          if (category === 'all' || script.getAttribute('data-category') === category) {
            script.style.display = 'flex';
          } else {
            script.style.display = 'none';
          }
        });
        
        // Hide all open script boxes
        document.querySelectorAll('.script-box').forEach(box => {
          box.style.display = 'none';
        });
        
        document.querySelectorAll('.script-title').forEach(title => {
          title.classList.remove('active');
        });
      });
    });
    
    // Form submission
    document.getElementById('access-form').addEventListener('submit', function(e) {
      e.preventDefault();
      const email = this.querySelector('input[type="email"]').value;
      
      // In a real implementation, you would send this to your backend
      console.log('Email submitted:', email);
      
      // Show success message
      alert('Thanks for your interest! Check your email for access to all 30 TrainerScripts templates.');
      
      // Clear the form
      this.reset();
    });
  </script>

</body>
</html>
