<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Theme Personal Trainer Client Communication Guide</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        // You can add custom colors here if needed
                    }
                }
            }
        }
    </script>

    <style>

        /* Theme-specific styles */
        [data-theme="light"] {
            --bg-primary: #ffffff;
            --bg-secondary: #f3f4f6;
            --bg-accent: #fef3c7;
            --text-primary: #1a1a1a;
            --text-secondary: #4b5563;
            --border-accent: #f7931a;
            --border-secondary: #d1d5db;
        }
        [data-theme="dark"] {
            --bg-primary: #1f2937;
            --bg-secondary: #374151;
            --bg-accent: #7f1d1d;
            --text-primary: #ffffff;
            --text-secondary: #d1d5db;
            --border-accent: #f7931a;
            --border-secondary: #4b5563;
        }
        [data-theme="matrix"] {
            --bg-primary: #000000;
            --bg-secondary: #1a1a1a;
            --bg-accent: #013220;
            --text-primary: #00ff00;
            --text-secondary: #22c55e;
            --border-accent: #00ff00;
            --border-secondary: #15803d;
            font-family: 'Courier New', Courier, monospace;
        }
        [data-theme="cyberpunk"] {
            --bg-primary: #2d1b4e;
            --bg-secondary: #3b2c6a;
            --bg-accent: #4c1d95;
            --text-primary: #c084fc;
            --text-secondary: #60a5fa;
            --border-accent: #c084fc;
            --border-secondary: #7c3aed;
        }

        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.3s ease;
        }
        .container {
            background-color: var(--bg-primary);
        }
        .script-box {
            background-color: var(--bg-secondary);
            border-left: 4px solid var(--border-accent);
        }
        .notes {
            background-color: var(--bg-accent);
            border-left: 4px solid var(--border-accent);
        }
        .script-box textarea {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            border-color: var(--border-secondary);
        }
        .script-box textarea:focus {
            --tw-ring-color: var(--border-accent);
        }
        h1, h2 {
            border-bottom-color: var(--border-accent);
        }
        .signature {
            border-top-color: var(--border-secondary);
        }

        /* Print-friendly styles (force light theme) */
        @media print {
            body {
                background-color: #ffffff;
            }
            .container {
                background-color: #ffffff;
                box-shadow: none;
                border: none;
            }
            .script-box {
                background-color: #f3f4f6;
                border-left: 4px solid #d1d5db;
            }
            .notes {
                background-color: #fef3c7;
                border-left: 4px solid #f7931a;
            }
            .script-box textarea, .notes, p, li {
                color: #4b5563;
            }
            h1, h2, .script-box p, .script-box li {
                color: #1a1a1a;
            }
        }
    </style>
</head>
<body class="font-sans">
    <div class="container mx-auto max-w-3xl p-6 rounded-lg shadow-lg my-6">
        <!-- Theme Toggle Button -->
        <div class="flex justify-end mb-4">
            <button id="theme-toggle" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-full flex items-center space-x-2">
                <span id="theme-icon">🌙</span>
                <span>Switch Theme</span>
            </button>
        </div>

        <h1 class="text-3xl font-bold text-center mb-6 border-b-4 pb-2">
            Personal Trainer Client Communication Guide
        </h1>

        <div class="mb-6">
            <p><strong>Purpose</strong>: This guide helps personal trainers discuss dietary changes and weight loss stalls with clients in a professional, empathetic, and motivating way. It ensures clear communication, client engagement, and personalized recommendations, especially for clients with significant weight loss goals or health concerns.</p>
            <p class="mt-2"><strong>Instructions for Trainers</strong>: Use this template during consultations or check-ins to address nutrition and progress. Customize the script based on the client’s profile (e.g., goals, lifestyle, personality). Convert to a PDF for staff training or reference.</p>
        </div>

        <div class="mb-6">
            <h2 class="text-2xl font-semibold border-b-2 pb-2 mb-4">1. Opening the Conversation</h2>
            <p><strong>Goal</strong>: Build trust and set a collaborative tone.</p>
            <div class="script-box p-4 my-4 rounded">
                <p class="font-semibold">Script:</p>
                <p>“[Client Name], you’re doing great work toward [their goal, e.g., losing 120 lbs, boosting energy]. I’ve noticed [observation, e.g., a weight loss stall, reliance on sugary foods]. Let’s work together to fine-tune your plan and keep you moving forward. How do you feel about your progress so far?”</p>
                <label for="script1" class="block font-medium mt-4">Your Script:</label>
                <textarea id="script1" class="w-full p-3 border rounded mt-2 resize-y focus:ring-2" placeholder="Enter your customized script here..."></textarea>
            </div>
            <div class="notes p-4 rounded italic">
                <p><em>Note: Use positive language, avoid blame, and invite client input to foster collaboration.</em></p>
            </div>
        </div>

        <div class="mb-6">
            <h2 class="text-2xl font-semibold border-b-2 pb-2 mb-4">2. Assessing the Situation</h2>
            <p><strong>Goal</strong>: Gather data on diet, exercise, and lifestyle to identify stall causes.</p>
            <div class="script-box p-4 my-4 rounded">
                <p class="font-semibold">Questions to Ask:</p>
                <ul class="list-disc pl-5">
                    <li>“Can you walk me through your typical meals and snacks? How do they affect your energy or hunger?”</li>
                    <li>“Have you been consistent with our [frequency, e.g., 3x/week] training sessions?”</li>
                    <li>“How’s your [sleep/stress/daily routine, e.g., night shift schedule] been lately?”</li>
                    <li>“Have you noticed changes in your weight or measurements over the past [timeframe, e.g., 4 weeks]?”</li>
                </ul>
                <label for="questions2" class="block font-medium mt-4">Your Questions:</label>
                <textarea id="questions2" class="w-full p-3 border rounded mt-2 resize-y focus:ring-2" placeholder="Enter your customized questions here..."></textarea>
            </div>
            <div class="notes p-4 rounded italic">
                <p><em>Note: Listen actively and note factors like caloric intake, adherence, or external stressors.</em></p>
            </div>
        </div>

        <div class="mb-6">
            <h2 class="text-2xl font-semibold border-b-2 pb-2 mb-4">3. Proposing Dietary Changes</h2>
            <p><strong>Goal</strong>: Suggest personalized, sustainable nutrition adjustments tied to the client’s goals.</p>
            <div class="script-box p-4 my-4 rounded">
                <p class="font-semibold">Script:</p>
                <p>“Based on what you’ve shared, [summarize issue, e.g., sugary breakfasts may cause energy crashes and slow weight loss]. I recommend [specific change, e.g., swapping sugary foods for a balanced breakfast with protein and complex carbs] to help you [benefit, e.g., feel energized and lose 1–2 lbs/week]. Here’s a plan we can try for [timeframe, e.g., 4 weeks]: [outline plan, e.g., 3–4 meals daily, including lean meats and controlled fruit servings]. What do you think about this approach?”</p>
                <label for="script3" class="block font-medium mt-4">Your Script:</label>
                <textarea id="script3" class="w-full p-3 border rounded mt-2 resize-y focus:ring-2" placeholder="Enter your customized script here..."></textarea>
            </div>
            <div class="script-box p-4 my-4 rounded">
                <p class="font-semibold">Sample Meal Plan:</p>
                <ul class="list-disc pl-5">
                    <li>Meal 1: ______________________________ (~___ kcal)</li>
                    <li>Meal 2: ______________________________ (~___ kcal)</li>
                    <li>Meal 3: ______________________________ (~___ kcal)</li>
                    <li>Optional: ______________________________ (~___ kcal)</li>
                </ul>
                <label for="mealplan3" class="block font-medium mt-4">Your Meal Plan:</label>
                <textarea id="mealplan3" class="w-full p-3 border rounded mt-2 resize-y focus:ring-2" placeholder="Enter your customized meal plan here..."></textarea>
            </div>
            <div class="notes p-4 rounded italic">
                <p><em>Note: Tailor to client’s habits, schedule, and preferences. Emphasize benefits like energy or health. Ensure a caloric deficit for weight loss, balanced macronutrients, and schedule compatibility.</em></p>
            </div>
        </div>

        <div class="mb-6">
            <h2 class="text-2xl font-semibold border-b-2 pb-2 mb-4">4. Integrating with Training</h2>
            <p><strong>Goal</strong>: Adjust exercise to support dietary changes and break the stall.</p>
            <div class="script-box p-4 my-4 rounded">
                <p class="font-semibold">Script:</p>
                <p>“To complement your new nutrition plan, let’s [adjustment, e.g., add a bit more cardio intensity or a home workout] to boost your calorie burn while keeping it safe for [health concern, e.g., your back]. We’ll stick with [current plan, e.g., 45-minute sessions, 3x/week] and monitor how you feel. Does that sound doable?”</p>
                <label for="script4" class="block font-medium mt-4">Your Script:</label>
                <textarea id="script4" class="w-full p-3 border rounded mt-2 resize-y focus:ring-2" placeholder="Enter your customized script here..."></textarea>
            </div>
            <div class="notes p-4 rounded italic">
                <p><em>Note: Ensure exercises are low-impact if injuries are present and align with energy goals.</em></p>
            </div>
        </div>

        <div class="mb-6">
            <h2 class="text-2xl font-semibold border-b-2 pb-2 mb-4">5. Motivating and Supporting</h2>
            <p><strong>Goal</strong>: Connect changes to the client’s motivations and provide ongoing support.</p>
            <div class="script-box p-4 my-4 rounded">
                <p class="font-semibold">Script:</p>
                <p>“These changes will help you [client’s motivation, e.g., live longer, feel stronger for your girlfriend]. I’ll check in weekly to see how you’re doing with [specific aspect, e.g., the new breakfast, energy levels]. We’ll celebrate milestones like [example, e.g., every 5 lbs lost] to keep you motivated. What’s one thing you’re excited about trying?”</p>
                <label for="script5" class="block font-medium mt-4">Your Script:</label>
                <textarea id="script5" class="w-full p-3 border rounded mt-2 resize-y focus:ring-2" placeholder="Enter your customized script here..."></textarea>
            </div>
            <div class="notes p-4 rounded italic">
                <p><em>Note: Tie to intrinsic motivators and set clear, achievable milestones.</em></p>
            </div>
        </div>

        <div class="mb-6">
            <h2 class="text-2xl font-semibold border-b-2 pb-2 mb-4">6. Monitoring and Follow-Up</h2>
            <p><strong>Goal</strong>: Track progress and adjust the plan as needed.</p>
            <div class="script-box p-4 my-4 rounded">
                <p class="font-semibold">Script:</p>
                <p>“Let’s track [metrics, e.g., weight, energy, adherence] over the next [timeframe, e.g., 4 weeks]. If we don’t see progress, we’ll tweak [aspect, e.g., calories, exercise]. Can we schedule a check-in for [date/time]? Feel free to reach out with any questions!”</p>
                <label for="script6" class="block font-medium mt-4">Your Script:</label>
                <textarea id="script6" class="w-full p-3 border rounded mt-2 resize-y focus:ring-2" placeholder="Enter your customized script here..."></textarea>
            </div>
            <div class="notes p-4 rounded italic">
                <p><em>Note: Set specific metrics and a follow-up date to maintain accountability.</em></p>
            </div>
        </div>

        <div class="mb-6">
            <h2 class="text-2xl font-semibold border-b-2 pb-2 mb-4">Notes for Trainers</h2>
            <div class="notes p-4 rounded italic">
                <ul class="list-disc pl-5">
                    <li><strong>Professionalism</strong>: Avoid negative or judgmental language (e.g., “your diet sucks”). Focus on solutions and client strengths.</li>
                    <li><strong>Personalization</strong>: Adapt scripts to the client’s personality (e.g., reserved clients need encouragement), schedule, and health needs.</li>
                    <li><strong>Evidence-Based</strong>: Base dietary advice on balanced nutrition, not extreme trends (e.g., avoid prolonged fasting without medical supervision).</li>
                    <li><strong>PDF Conversion</strong>: Copy into a word processor, add your logo, and export as a PDF for staff training or client handouts.</li>
                    <li><strong>Compliance</strong>: Ensure dietary advice aligns with scope of practice and local regulations (e.g., consult a dietitian for complex cases).</li>
                </ul>
            </div>
        </div>

        <div class="signature border-t pt-4 text-right">
            <p><strong>Trainer’s Name</strong>: ______________________________</p>
            <p><strong>Date</strong>: ______________________________</p>
        </div>
    </div>

    <script>
        // Theme Toggle Functionality
        const toggleButton = document.getElementById('theme-toggle');
        const themeIcon = document.getElementById('theme-icon');
        const htmlElement = document.documentElement;
        const themes = ['light', 'dark', 'matrix', 'cyberpunk'];
        const icons = {
            light: '🌙',
            dark: '☀️',
            matrix: '💾',
            cyberpunk: '🌌'
        };

        // Load saved theme
        const savedTheme = localStorage.getItem('theme') || 'light';
        htmlElement.setAttribute('data-theme', savedTheme);
        themeIcon.textContent = icons[savedTheme];

        toggleButton.addEventListener('click', () => {
            const currentTheme = htmlElement.getAttribute('data-theme');
            const currentIndex = themes.indexOf(currentTheme);
            const nextIndex = (currentIndex + 1) % themes.length;
            const nextTheme = themes[nextIndex];

            htmlElement.setAttribute('data-theme', nextTheme);
            themeIcon.textContent = icons[nextTheme];
            localStorage.setItem('theme', nextTheme);
        });
    </script>
</body>
</html>